2025-08-01 10:24:07,904 - Log<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> initialized using file 'velocity.log'
2025-08-01 10:24:07,904 - Initializing Velocity, Calling init()...
2025-08-01 10:24:07,904 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-01 10:24:07,904 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-01 10:24:07,904 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-01 10:24:07,904 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-01 10:24:07,904 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-01 10:24:07,906 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-01 10:24:07,912 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-08-01 10:24:07,915 - Do unicode file recognition:  false
2025-08-01 10:24:07,915 - FileResourceLoader : adding path '.'
2025-08-01 10:24:07,929 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-01 10:24:07,933 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-01 10:24:07,935 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-01 10:24:07,936 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-01 10:24:07,937 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-01 10:24:07,938 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-01 10:24:07,939 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-01 10:24:07,941 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-01 10:24:07,942 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-01 10:24:07,944 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-01 10:24:07,964 - Created '20' parsers.
2025-08-01 10:24:07,967 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-01 10:24:07,967 - Velocimacro : Default library not found.
2025-08-01 10:24:07,967 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-01 10:24:07,967 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-01 10:24:07,967 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-01 10:24:07,967 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-08-01 11:28:46,421 - Log4JLogChute initialized using file 'velocity.log'
2025-08-01 11:28:46,421 - Initializing Velocity, Calling init()...
2025-08-01 11:28:46,421 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-01 11:28:46,421 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-01 11:28:46,421 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-01 11:28:46,421 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-01 11:28:46,421 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-01 11:28:46,421 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-01 11:28:46,425 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-08-01 11:28:46,426 - Do unicode file recognition:  false
2025-08-01 11:28:46,426 - FileResourceLoader : adding path '.'
2025-08-01 11:28:46,435 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-01 11:28:46,438 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-01 11:28:46,439 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-01 11:28:46,439 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-01 11:28:46,440 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-01 11:28:46,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-01 11:28:46,441 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-01 11:28:46,442 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-01 11:28:46,443 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-01 11:28:46,444 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-01 11:28:46,455 - Created '20' parsers.
2025-08-01 11:28:46,456 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-01 11:28:46,456 - Velocimacro : Default library not found.
2025-08-01 11:28:46,456 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-01 11:28:46,456 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-01 11:28:46,457 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-01 11:28:46,457 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-08-02 10:02:20,560 - Log4JLogChute initialized using file 'velocity.log'
2025-08-02 10:02:20,561 - Initializing Velocity, Calling init()...
2025-08-02 10:02:20,561 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-02 10:02:20,561 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-02 10:02:20,561 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-02 10:02:20,561 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-02 10:02:20,561 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-02 10:02:20,561 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-02 10:02:20,565 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-08-02 10:02:20,566 - Do unicode file recognition:  false
2025-08-02 10:02:20,566 - FileResourceLoader : adding path '.'
2025-08-02 10:02:20,581 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-02 10:02:20,583 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-02 10:02:20,584 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-02 10:02:20,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-02 10:02:20,585 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-02 10:02:20,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-02 10:02:20,586 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-02 10:02:20,588 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-02 10:02:20,588 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-02 10:02:20,589 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-02 10:02:20,602 - Created '20' parsers.
2025-08-02 10:02:20,606 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-02 10:02:20,606 - Velocimacro : Default library not found.
2025-08-02 10:02:20,606 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-02 10:02:20,606 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-02 10:02:20,606 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-02 10:02:20,606 - Velocimacro : autoload off : VM system will not automatically reload global library macros
2025-08-02 10:34:37,737 - Log4JLogChute initialized using file 'velocity.log'
2025-08-02 10:34:37,738 - Initializing Velocity, Calling init()...
2025-08-02 10:34:37,738 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-08-02 10:34:37,738 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-08-02 10:34:37,738 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-08-02 10:34:37,738 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-08-02 10:34:37,738 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-02 10:34:37,738 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-08-02 10:34:37,741 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-08-02 10:34:37,742 - Do unicode file recognition:  false
2025-08-02 10:34:37,742 - FileResourceLoader : adding path '.'
2025-08-02 10:34:37,751 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-08-02 10:34:37,754 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-08-02 10:34:37,755 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-08-02 10:34:37,755 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-08-02 10:34:37,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-08-02 10:34:37,756 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-08-02 10:34:37,757 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-08-02 10:34:37,758 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-08-02 10:34:37,758 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-08-02 10:34:37,759 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-08-02 10:34:37,772 - Created '20' parsers.
2025-08-02 10:34:37,774 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-08-02 10:34:37,774 - Velocimacro : Default library not found.
2025-08-02 10:34:37,775 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-08-02 10:34:37,775 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-08-02 10:34:37,775 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-08-02 10:34:37,775 - Velocimacro : autoload off : VM system will not automatically reload global library macros
