<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.edt.mapper.SaQuestionbankitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo">
        <include refid="selectSaQuestionbankitemVo"/>
        where Sa_QuestionBankItem.id = #{key} 
    </select>
    <sql id="selectSaQuestionbankitemVo">
         select
id, Pid, FormItemId, Type, Label, TextLabel, IsDisplayType, IsHideType, IsSpecialType, ShowLabel, DefaultValue, Required, Placeholder, Sort, Span, Scheme, RegList, ShowColumn, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>antid, Revision        from Sa_QuestionBankItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo">
        <include refid="selectSaQuestionbankitemVo"/>
        where Sa_QuestionBankItem.Pid = #{Pid}
        order by Sort
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo">
        <include refid="selectSaQuestionbankitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_QuestionBankItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_QuestionBankItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.formitemid != null and SearchPojo.formitemid != ''">
   and Sa_QuestionBankItem.formitemid like concat('%', #{SearchPojo.formitemid}, '%')
</if>
<if test="SearchPojo.type != null and SearchPojo.type != ''">
   and Sa_QuestionBankItem.type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.label != null and SearchPojo.label != ''">
   and Sa_QuestionBankItem.label like concat('%', #{SearchPojo.label}, '%')
</if>
<if test="SearchPojo.textlabel != null and SearchPojo.textlabel != ''">
   and Sa_QuestionBankItem.textlabel like concat('%', #{SearchPojo.textlabel}, '%')
</if>
<if test="SearchPojo.defaultvalue != null and SearchPojo.defaultvalue != ''">
   and Sa_QuestionBankItem.defaultvalue like concat('%', #{SearchPojo.defaultvalue}, '%')
</if>
<if test="SearchPojo.placeholder != null and SearchPojo.placeholder != ''">
   and Sa_QuestionBankItem.placeholder like concat('%', #{SearchPojo.placeholder}, '%')
</if>
<if test="SearchPojo.scheme != null and SearchPojo.scheme != ''">
   and Sa_QuestionBankItem.scheme like concat('%', #{SearchPojo.scheme}, '%')
</if>
<if test="SearchPojo.reglist != null and SearchPojo.reglist != ''">
   and Sa_QuestionBankItem.reglist like concat('%', #{SearchPojo.reglist}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_QuestionBankItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_QuestionBankItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_QuestionBankItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_QuestionBankItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_QuestionBankItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_QuestionBankItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.formitemid != null and SearchPojo.formitemid != ''">
   or Sa_QuestionBankItem.FormItemId like concat('%', #{SearchPojo.formitemid}, '%')
</if>
<if test="SearchPojo.type != null and SearchPojo.type != ''">
   or Sa_QuestionBankItem.Type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.label != null and SearchPojo.label != ''">
   or Sa_QuestionBankItem.Label like concat('%', #{SearchPojo.label}, '%')
</if>
<if test="SearchPojo.textlabel != null and SearchPojo.textlabel != ''">
   or Sa_QuestionBankItem.TextLabel like concat('%', #{SearchPojo.textlabel}, '%')
</if>
<if test="SearchPojo.defaultvalue != null and SearchPojo.defaultvalue != ''">
   or Sa_QuestionBankItem.DefaultValue like concat('%', #{SearchPojo.defaultvalue}, '%')
</if>
<if test="SearchPojo.placeholder != null and SearchPojo.placeholder != ''">
   or Sa_QuestionBankItem.Placeholder like concat('%', #{SearchPojo.placeholder}, '%')
</if>
<if test="SearchPojo.scheme != null and SearchPojo.scheme != ''">
   or Sa_QuestionBankItem.Scheme like concat('%', #{SearchPojo.scheme}, '%')
</if>
<if test="SearchPojo.reglist != null and SearchPojo.reglist != ''">
   or Sa_QuestionBankItem.RegList like concat('%', #{SearchPojo.reglist}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_QuestionBankItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_QuestionBankItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_QuestionBankItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_QuestionBankItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_QuestionBankItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_QuestionBankItem(id, Pid, FormItemId, Type, Label, TextLabel, IsDisplayType, IsHideType, IsSpecialType, ShowLabel, DefaultValue, Required, Placeholder, Sort, Span, Scheme, RegList, ShowColumn, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{formitemid}, #{type}, #{label}, #{textlabel}, #{isdisplaytype}, #{ishidetype}, #{isspecialtype}, #{showlabel}, #{defaultvalue}, #{required}, #{placeholder}, #{sort}, #{span}, #{scheme}, #{reglist}, #{showcolumn}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_QuestionBankItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="formitemid != null ">
                FormItemId = #{formitemid},
            </if>
            <if test="type != null ">
                Type = #{type},
            </if>
            <if test="label != null ">
                Label = #{label},
            </if>
            <if test="textlabel != null ">
                TextLabel = #{textlabel},
            </if>
            <if test="isdisplaytype != null">
                IsDisplayType = #{isdisplaytype},
            </if>
            <if test="ishidetype != null">
                IsHideType = #{ishidetype},
            </if>
            <if test="isspecialtype != null">
                IsSpecialType = #{isspecialtype},
            </if>
            <if test="showlabel != null">
                ShowLabel = #{showlabel},
            </if>
            <if test="defaultvalue != null ">
                DefaultValue = #{defaultvalue},
            </if>
            <if test="required != null">
                Required = #{required},
            </if>
            <if test="placeholder != null ">
                Placeholder = #{placeholder},
            </if>
            <if test="sort != null">
                Sort = #{sort},
            </if>
            <if test="span != null">
                Span = #{span},
            </if>
            <if test="scheme != null ">
                Scheme = #{scheme},
            </if>
            <if test="reglist != null ">
                RegList = #{reglist},
            </if>
            <if test="showcolumn != null">
                ShowColumn = #{showcolumn},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_QuestionBankItem where id = #{key} 
    </delete>

    <select id="getByFormItemId" resultType="inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo">
        <include refid="selectSaQuestionbankitemVo"/>
        where Sa_QuestionBankItem.FormItemId = #{formItemId}
        and Sa_QuestionBankItem.Pid = #{formId}
    </select>
</mapper>

