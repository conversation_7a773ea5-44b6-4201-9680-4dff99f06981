<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.edt.mapper.SaFormauthMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.edt.domain.pojo.SaFormauthPojo">
        select id,
               FormId,
               AuthGroupId,
               UserIdList,
               RoleIdList,
               DeptIdList,
               Remark,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision
        from Sa_FormAuth
        where Sa_FormAuth.id = #{key}
    </select>
    <sql id="selectSaFormauthVo">
        select id,
               FormId,
               AuthGroupId,
               UserId<PERSON>ist,
               <PERSON>Id<PERSON><PERSON>,
               DeptIdList,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>yi<PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON>er,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               <PERSON>antid,
               Revision
        from Sa_FormAuth
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.edt.domain.pojo.SaFormauthPojo">
        <include refid="selectSaFormauthVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}



        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_FormAuth.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.formid != null">
            and Sa_FormAuth.FormId like concat('%', #{SearchPojo.formid}, '%')
        </if>
        <if test="SearchPojo.authgroupid != null">
            and Sa_FormAuth.AuthGroupId like concat('%',
                #{SearchPojo.authgroupid}, '%')
        </if>
        <if test="SearchPojo.useridlist != null">
            and Sa_FormAuth.UserIdList like concat('%',
                #{SearchPojo.useridlist}, '%')
        </if>
        <if test="SearchPojo.roleidlist != null">
            and Sa_FormAuth.RoleIdList like concat('%',
                #{SearchPojo.roleidlist}, '%')
        </if>
        <if test="SearchPojo.deptidlist != null">
            and Sa_FormAuth.DeptIdList like concat('%',
                #{SearchPojo.deptidlist}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_FormAuth.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_FormAuth.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_FormAuth.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_FormAuth.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_FormAuth.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_FormAuth.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_FormAuth.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_FormAuth.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_FormAuth.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_FormAuth.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.realdata != null">
            and Sa_FormData.RealData like concat('%',
                #{SearchPojo.realdata}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.formid != null">
                or Sa_FormAuth.FormId like concat('%', #{SearchPojo.formid}, '%')
            </if>
            <if test="SearchPojo.authgroupid != null">
                or Sa_FormAuth.AuthGroupId like concat('%', #{SearchPojo.authgroupid}, '%')
            </if>
            <if test="SearchPojo.useridlist != null">
                or Sa_FormAuth.UserIdList like concat('%', #{SearchPojo.useridlist}, '%')
            </if>
            <if test="SearchPojo.roleidlist != null">
                or Sa_FormAuth.RoleIdList like concat('%', #{SearchPojo.roleidlist}, '%')
            </if>
            <if test="SearchPojo.deptidlist != null">
                or Sa_FormAuth.DeptIdList like concat('%', #{SearchPojo.deptidlist}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_FormAuth.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_FormAuth.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_FormAuth.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_FormAuth.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_FormAuth.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_FormAuth.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_FormAuth.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_FormAuth.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_FormAuth.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_FormAuth.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.realdata != null">
                or Sa_FormData.RealData like concat('%',#{SearchPojo.realdata}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_FormAuth(id, FormId, AuthGroupId, UserIdList, RoleIdList, DeptIdList, Remark, CreateByid,
                                CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4,
                                Custom5, Tenantid, Revision)
        values (#{id}, #{formid}, #{authgroupid}, #{useridlist}, #{roleidlist}, #{deptidlist}, #{remark}, #{createbyid},
                #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3},
                #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_FormAuth
        <set>
            <if test="formid != null">
                FormId =#{formid},
            </if>
            <if test="authgroupid != null">
                AuthGroupId =#{authgroupid},
            </if>
            <if test="useridlist != null">
                UserIdList =#{useridlist},
            </if>
            <if test="roleidlist != null">
                RoleIdList =#{roleidlist},
            </if>
            <if test="deptidlist != null">
                DeptIdList =#{deptidlist},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_FormAuth
        where id = #{key}
    </delete>

    <select id="getAuthUsersAndDepts" resultType="inks.service.sa.edt.domain.vo.AuthUsersAndDeptsVO">
        SELECT F.FormAuthId,
               JSON_UNQUOTE(JSON_EXTRACT(F.UseridList, CONCAT('$[', J.idx, ']'))) AS UserId,
               U.UserName
        FROM Sa_FormAuth F
                 CROSS JOIN JSON_TABLE(
                F.UseridList,
                '$[*]' COLUMNS (
                    idx FOR ORDINALITY,
                    UserId INT PATH '$'
                    )
            ) AS J
                 LEFT JOIN Sa_User U ON J.UserId = U.id;
    </select>

    <select id="getEntityByFormId" resultType="inks.service.sa.edt.domain.pojo.SaFormauthPojo">
        select *
        from Sa_FormAuth
        where FormId = #{formid}
    </select>

    <select id="getUserInfo" resultType="java.util.Map">
        select id,
               UserName,
               RealName
        from Sa_User
        where id
        <foreach collection="useridList" item="userid" index="index" open="in (" separator="," close=")">
            #{userid}
        </foreach>
    </select>

    <select id="getDeptInfo" resultType="java.util.Map">
        select id,
               DeptName
        from Sa_Dept
        where id
        <foreach collection="deptidList" item="deptid" index="index" open="in (" separator="," close=")">
            #{deptid}
        </foreach>
    </select>


    <!--    <select id="getAllFormByUserid" resultType="inks.service.sa.edt.domain.pojo.SaFormPojo">-->
    <!--        select * from-->
    <!--        (SELECT FormId, UserId-->
    <!--        FROM Sa_FormAuth,-->
    <!--             JSON_TABLE(UserIdList, '$[*]' COLUMNS (UserId VARCHAR(255) PATH '$')) AS jt) as fu-->
    <!--    </select>-->

    <select id="getAllFormIdByUserid" resultType="String">
        SELECT DISTINCT FormId
        FROM Sa_FormAuth
        WHERE JSON_SEARCH(UserIdList, 'one', #{userid}) IS NOT NULL;
    </select>

    <!--    where id in 上面的语句-->
    <select id="getAllFormByUserid" resultType="inks.service.sa.edt.domain.pojo.SaFormPojo">
        select *
        from Sa_Form
        where id in (SELECT DISTINCT FormId
                     FROM Sa_FormAuth
                     WHERE JSON_SEARCH(UserIdList, 'one', #{userid}) IS NOT NULL)
    </select>


    <!--    where id in 上面的语句 -->
<!--    20231115改成了getAllFormByUserid的功能-->
    <select id="getUnWriteFormByUserid" resultType="inks.service.sa.edt.domain.pojo.SaFormPojo">
        SELECT *
        FROM Sa_Form
        WHERE id IN (SELECT DISTINCT FormId
                     FROM Sa_FormAuth
                     WHERE (JSON_SEARCH(UserIdList, 'one', #{userid}) IS NOT NULL
                         OR JSON_EXTRACT(DeptIdList, '$[*]') REGEXP
                            (SELECT CONCAT('(', GROUP_CONCAT(Deptid SEPARATOR '|'), ')')
                             FROM Sa_DeptUser
                             WHERE Userid = #{userid})
                               ))
          AND Status = 2
    </select>
<!--    <select id="OldgetUnWriteFormByUserid" resultType="inks.service.sa.edt.domain.pojo.SaFormPojo">-->
<!--        SELECT *-->
<!--        FROM Sa_Form-->
<!--        WHERE id IN (SELECT DISTINCT FormId-->
<!--                     FROM Sa_FormAuth-->
<!--                     WHERE (JSON_SEARCH(UserIdList, 'one', #{userid}) IS NOT NULL-->
<!--                         OR JSON_EXTRACT(DeptIdList, '$[*]') REGEXP-->
<!--                            (SELECT CONCAT('(', GROUP_CONCAT(Deptid SEPARATOR '|'), ')')-->
<!--                             FROM Sa_DeptUser-->
<!--                             WHERE Userid = #{userid})-->
<!--                               ))-->
<!--          AND NOT EXISTS (SELECT 1-->
<!--                          FROM Sa_FormData AS fd-->
<!--                          WHERE fd.FormId = Sa_Form.id-->
<!--                            AND fd.CreateByid = #{userid})-->
<!--          AND Status = 2-->
<!--    </select>-->

    <select id="countAllUsersUnWrite" resultType="int">
        SELECT count(1)
        FROM Sa_Form
        WHERE id IN (SELECT DISTINCT FormId
                     FROM Sa_FormAuth
                     WHERE (JSON_SEARCH(UserIdList, 'one', #{userid}) IS NOT NULL
                         OR JSON_EXTRACT(DeptIdList, '$[*]') REGEXP
                            (SELECT CONCAT('(', GROUP_CONCAT(Deptid SEPARATOR '|'), ')')
                             FROM Sa_DeptUser
                             WHERE Userid = #{userid})
                               ))
          AND NOT EXISTS (SELECT 1
                          FROM Sa_FormData AS fd
                          WHERE fd.FormId = Sa_Form.id
                            AND fd.CreateByid = #{userid})
          AND Status = 2
    </select>


    <select id="getUnWriteFormByUserid原始" resultType="inks.service.sa.edt.domain.pojo.SaFormPojo">
        select *
        from Sa_Form
        where id in (SELECT DISTINCT FormId
                     FROM Sa_FormAuth
                     WHERE JSON_SEARCH(UserIdList, 'one', #{userid}) IS NOT NULL)
          and not EXISTS(select id from Sa_FormData where FormId = Sa_Form.id and UserId = #{userid})
          and Status = 2
    </select>


    <!--    获取条件下userId关联Form主表信息List type:uDeptIdListnwrite未填写FormDat、unsubmit已填写未提交、submit已填写已提交、reject已提交但审核驳回、pass已提交且审核通过-->
    <!--    DPCI#查询改为RealData数据 (select Sa_FormItem.FormItemId from Sa_FormItem where Sa_FormItem.Label LIKE '%DPCI#%' and Sa_FormItem.Pid = Sa_Form.Id) as formitemid-->
    <select id="getSomeFormByUserid" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.edt.domain.pojo.SaFormdataPojo">
        SELECT Sa_FormData.*,
               Sa_Form.Description as formdesc,
               Sa_Form.Name        as formname
        FROM Sa_Form
                 left join Sa_FormData on Sa_Form.Id = Sa_FormData.FormId
        WHERE Sa_Form.Status = 2
        <if test="isAdmin == false">
            and Sa_FormData.CreateByid
            <foreach collection="useridList" item="userid" index="index" open="in (" separator="," close=")">
                #{userid}
            </foreach>
        </if>
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_FormData.ModifyDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and #{queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        <if test="type == 'unsubmit'">
            and Sa_FormData.Submit = 0
        </if>
        <if test="type == 'submit'">
            and Sa_FormData.Submit = 1
        </if>
        <if test="type == 'reject'">
            and Sa_FormData.Submit = 1
            and Sa_FormData.AssessStatus = '驳回'
        </if>
        <if test="type == 'pass'">
            and Sa_FormData.Submit = 1
            and Sa_FormData.AssessStatus = '通过'
        </if>
        order by ${queryParam.orderBy}
        <if test="queryParam.OrderType == 0">
            asc
        </if>
        <if test="queryParam.OrderType == 1">
            desc
        </if>
    </select>

    <!--    获取条件下userId关联Form主表信息List type:uDeptIdListnwrite未填写FormDat、unsubmit已填写未提交、submit已填写已提交、reject已提交但审核驳回、pass已提交且审核通过-->
    <select id="getSomeFormByUserid原版" resultType="inks.service.sa.edt.domain.pojo.SaFormdataPojo">
        select Sa_FormData.*,
               Sa_Form.Name        as formname,
               Sa_Form.Description as formdesc
        from Sa_Form
                 left join Sa_FormData on Sa_Form.Id = Sa_FormData.FormId
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}


        </if>
        <if test="type == 'unwrite'">
            and Sa_FormData.id is null
        </if>
        <if test="type == 'unsubmit'">
            and Sa_FormData.Submit = 0
        </if>
        <if test="type == 'submit'">
            and Sa_FormData.Submit = 1
        </if>
        <if test="type == 'reject'">
            and Sa_FormData.Submit = 1
            and Sa_FormData.AssessStatus = '驳回'
        </if>
        <if test="type == 'pass'">
            and Sa_FormData.Submit = 1
            and Sa_FormData.AssessStatus = '通过'
        </if>
        and Sa_Form.id in (SELECT DISTINCT FormId
                           FROM Sa_FormAuth
                           WHERE JSON_SEARCH(UserIdList, 'one', #{userid}) IS NOT NULL)
        and Status = 2
        order by Sa_FormData.ModifyDate desc
    </select>
</mapper>

