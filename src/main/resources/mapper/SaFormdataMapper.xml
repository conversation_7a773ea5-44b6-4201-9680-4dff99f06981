<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.edt.mapper.SaFormdataMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.edt.domain.pojo.SaFormdataPojo">
        <include refid="selectSaFormdataVo"/>
        where Sa_FormData.id = #{key}
    </select>
    <sql id="selectSaFormdataVo">
        select Sa_FormData.id,
               Sa_FormData.FormId,
               Sa_FormData.SeqlNum,
               Sa_FormData.OriginalData,
               Sa_FormData.RealData,
               Sa_FormData.UserAgent,
               Sa_FormData.SubOs,
               Sa_FormData.SubBrowser,
               Sa_FormData.SubReqIp,
               Sa_FormData.SubAddr,
               Sa_FormData.CompleteTime,
               Sa_FormData.WxOpenId,
               Sa_FormData.WxUserInfo,
               Sa_FormData.ExtValue,
               Sa_FormData.Remark,
               Sa_FormData.Submit,
               Sa_FormData.TotalScore,
               Sa_FormData.SumTotalScore,
               Sa_FormData.ScoreDetail,
               Sa_FormData.Assessor,
               Sa_FormData.Assessorid,
               Sa_FormData.AssessDate,
               Sa_FormData.AssessStatus,
               Sa_FormData.CreateByid,
               Sa_FormData.CreateBy,
               Sa_FormData.CreateDate,
               Sa_FormData.Listerid,
               Sa_FormData.Lister,
               Sa_FormData.ModifyDate,
               Sa_FormData.Custom1,
               Sa_FormData.Custom2,
               Sa_FormData.Custom3,
               Sa_FormData.Custom4,
               Sa_FormData.Custom5,
               Sa_FormData.Deptid,
               Sa_FormData.Tenantid,
               Sa_FormData.Revision,
               Sa_Form.Name as formname
        from Sa_FormData
                 Left join Sa_Form on Sa_FormData.FormId = Sa_Form.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.edt.domain.pojo.SaFormdataPojo">
        <include refid="selectSaFormdataVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}

        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_FormData.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.formid != null">
            and Sa_FormData.FormId like concat('%', #{SearchPojo.formid}, '%')
        </if>
        <if test="SearchPojo.originaldata != null">
            and Sa_FormData.OriginalData like concat('%',
                #{SearchPojo.originaldata}, '%')
        </if>
        <if test="SearchPojo.useragent != null">
            and Sa_FormData.UserAgent like concat('%',
                #{SearchPojo.useragent}, '%')
        </if>
        <if test="SearchPojo.subos != null">
            and Sa_FormData.SubOs like concat('%',
                #{SearchPojo.subos}, '%')
        </if>
        <if test="SearchPojo.subbrowser != null">
            and Sa_FormData.SubBrowser like concat('%',
                #{SearchPojo.subbrowser}, '%')
        </if>
        <if test="SearchPojo.subreqip != null">
            and Sa_FormData.SubReqIp like concat('%',
                #{SearchPojo.subreqip}, '%')
        </if>
        <if test="SearchPojo.subaddr != null">
            and Sa_FormData.SubAddr like concat('%',
                #{SearchPojo.subaddr}, '%')
        </if>
        <if test="SearchPojo.wxopenid != null">
            and Sa_FormData.WxOpenId like concat('%',
                #{SearchPojo.wxopenid}, '%')
        </if>
        <if test="SearchPojo.wxuserinfo != null">
            and Sa_FormData.WxUserInfo like concat('%',
                #{SearchPojo.wxuserinfo}, '%')
        </if>
        <if test="SearchPojo.extvalue != null">
            and Sa_FormData.ExtValue like concat('%',
                #{SearchPojo.extvalue}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_FormData.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_FormData.Assessor like concat('%',
                #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_FormData.Assessorid like concat('%',
                #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessstatus != null">
            and Sa_FormData.AssessStatus like concat('%',
                #{SearchPojo.assessstatus}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_FormData.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_FormData.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_FormData.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_FormData.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_FormData.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_FormData.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_FormData.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_FormData.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_FormData.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.formid != null">
                or Sa_FormData.FormId like concat('%', #{SearchPojo.formid}, '%')
            </if>
            <if test="SearchPojo.originaldata != null">
                or Sa_FormData.OriginalData like concat('%', #{SearchPojo.originaldata}, '%')
            </if>
            <if test="SearchPojo.useragent != null">
                or Sa_FormData.UserAgent like concat('%', #{SearchPojo.useragent}, '%')
            </if>
            <if test="SearchPojo.subos != null">
                or Sa_FormData.SubOs like concat('%', #{SearchPojo.subos}, '%')
            </if>
            <if test="SearchPojo.subbrowser != null">
                or Sa_FormData.SubBrowser like concat('%', #{SearchPojo.subbrowser}, '%')
            </if>
            <if test="SearchPojo.subreqip != null">
                or Sa_FormData.SubReqIp like concat('%', #{SearchPojo.subreqip}, '%')
            </if>
            <if test="SearchPojo.subaddr != null">
                or Sa_FormData.SubAddr like concat('%', #{SearchPojo.subaddr}, '%')
            </if>
            <if test="SearchPojo.wxopenid != null">
                or Sa_FormData.WxOpenId like concat('%', #{SearchPojo.wxopenid}, '%')
            </if>
            <if test="SearchPojo.wxuserinfo != null">
                or Sa_FormData.WxUserInfo like concat('%', #{SearchPojo.wxuserinfo}, '%')
            </if>
            <if test="SearchPojo.extvalue != null">
                or Sa_FormData.ExtValue like concat('%', #{SearchPojo.extvalue}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_FormData.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_FormData.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_FormData.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessstatus != null">
                or Sa_FormData.AssessStatus like concat('%', #{SearchPojo.assessstatus}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_FormData.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_FormData.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_FormData.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_FormData.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_FormData.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_FormData.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_FormData.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_FormData.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_FormData.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_FormData(id, FormId, SeqlNum, OriginalData, RealData, UserAgent, SubOs, SubBrowser, SubReqIp, SubAddr, CompleteTime, WxOpenId, WxUserInfo, ExtValue, Remark, Submit, TotalScore, SumTotalScore, ScoreDetail, Assessor, Assessorid, AssessDate, AssessStatus, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, Revision)
        values (#{id}, #{formid}, #{seqlnum}, #{originaldata}, #{realdata}, #{useragent}, #{subos}, #{subbrowser}, #{subreqip}, #{subaddr}, #{completetime}, #{wxopenid}, #{wxuserinfo}, #{extvalue}, #{remark}, #{submit}, #{totalscore}, #{sumtotalscore}, #{scoredetail}, #{assessor}, #{assessorid}, #{assessdate}, #{assessstatus}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{deptid}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_FormData
        <set>
            <if test="formid != null">
                FormId =#{formid},
            </if>
            <if test="seqlnum != null">
                SeqlNum =#{seqlnum},
            </if>
            <if test="originaldata != null">
                OriginalData =#{originaldata},
            </if>
            <if test="realdata != null">
                RealData =#{realdata},
            </if>
            <if test="useragent != null">
                UserAgent =#{useragent},
            </if>
            <if test="subos != null">
                SubOs =#{subos},
            </if>
            <if test="subbrowser != null">
                SubBrowser =#{subbrowser},
            </if>
            <if test="subreqip != null">
                SubReqIp =#{subreqip},
            </if>
            <if test="subaddr != null">
                SubAddr =#{subaddr},
            </if>
            <if test="completetime != null">
                CompleteTime =#{completetime},
            </if>
            <if test="wxopenid != null">
                WxOpenId =#{wxopenid},
            </if>
            <if test="wxuserinfo != null">
                WxUserInfo =#{wxuserinfo},
            </if>
            <if test="extvalue != null">
                ExtValue =#{extvalue},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="submit != null">
                Submit =#{submit},
            </if>
            <if test="totalscore != null">
                TotalScore =#{totalscore},
            </if>
            <if test="sumtotalscore != null">
                SumTotalScore =#{sumtotalscore},
            </if>
            <if test="scoredetail != null ">
                ScoreDetail =#{scoredetail},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="assessstatus != null">
                AssessStatus =#{assessstatus},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_FormData
        where id = #{key}
    </delete>

    <select id="getCountByFormIdWxAndIpAndUseridAndTime" resultType="int">
        select count(*)
        from Sa_FormData
        where FormId = #{formId}
        <if test="wxOpenId != null and wxOpenId != ''">
            and WxOpenId = #{wxOpenId}
        </if>
        <if test="ipAddr != null and ipAddr != ''">
            and SubReqIp = #{ipAddr}
        </if>
        <if test="userId != null and userId != ''">
            and CreateByid = #{userId}
        </if>
        <if test="rangeTypeSql != null and rangeTypeSql != ''">
            #{rangeTypeSql}
        </if>
    </select>

    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_FormData
        SET Assessor     = #{assessor},
            Assessorid   = #{assessorid},
            AssessDate   = #{assessdate},
            AssessStatus = #{assessstatus},
            Revision=Revision + 1
        where id = #{id}
    </update>

    <!-- 查询以计算表单提交数量并返回结果为Java.util.Map的select语句 -->
    <select id="countFormdataSubmit" resultType="java.util.Map">
        SELECT sum(if(Submit = 1, 1, 0))                        as submitcount,
               sum(if(Submit = 1 &amp;&amp; SeqlNum = 1, 1, 0)) as distinctsubmitcount
        FROM Sa_FormData
    </select>

    <select id="formFilledInNumber" resultType="java.util.Map">
        SELECT DATE(CreateDate) AS date, COUNT(1) AS submitcount
        FROM Sa_FormData
        where Submit = 1
          and CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
        GROUP BY DATE(CreateDate)
    </select>

    <!--    表单总填写数（大于0的），按表单-->
    <select id="formSubmitNumberByForm" resultType="java.util.Map">
        SELECT Sa_FormData.FormId as formid,
               Sa_Form.Name       as formname,
               COUNT(1)           AS submitcount
        FROM Sa_FormData
                 left join Sa_Form on Sa_FormData.FormId = Sa_Form.id
        where Sa_FormData.Submit = 1
        GROUP BY Sa_FormData.FormId, Sa_Form.Name
        having submitcount > 0
        order by submitcount desc;
    </select>



    <select id="getAllNoRealData" resultType="inks.service.sa.edt.domain.pojo.SaFormdataPojo">
        SELECT *
        FROM Sa_FormData
        where Sa_FormData.RealData is null
    </select>

    <update id="updateRealData">
        update Sa_FormData
        SET RealData = #{realdata}
        where id = #{id}
    </update>
</mapper>

