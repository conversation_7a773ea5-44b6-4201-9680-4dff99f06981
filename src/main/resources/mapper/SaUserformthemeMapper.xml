<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.edt.mapper.SaUserformthemeMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.edt.domain.pojo.SaUserformthemePojo">
        select
          id, FormId, SubmitBtnText, LogoImg, LogoPosition, BackColor, BackImg, ShowTitle, ShowDesc, ThemeColor, ShowNumber, ShowSubmitBtn, HeadImgUrl, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_UserFormTheme
        where Sa_UserFormTheme.id = #{key} 
    </select>
    <sql id="selectSaUserformthemeVo">
         select
          id, FormId, SubmitBtnText, LogoImg, LogoPosition, BackColor, BackImg, ShowTitle, ShowDesc, ThemeColor, ShowNumber, ShowSubmitBtn, HeadImgUrl, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_UserFormTheme
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.edt.domain.pojo.SaUserformthemePojo">
        <include refid="selectSaUserformthemeVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_UserFormTheme.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and #{DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.formid != null ">
   and Sa_UserFormTheme.FormId like concat('%', #{SearchPojo.formid}, '%')
</if>
<if test="SearchPojo.submitbtntext != null ">
   and Sa_UserFormTheme.SubmitBtnText like concat('%', #{SearchPojo.submitbtntext}, '%')
</if>
<if test="SearchPojo.logoimg != null ">
   and Sa_UserFormTheme.LogoImg like concat('%', #{SearchPojo.logoimg}, '%')
</if>
<if test="SearchPojo.logoposition != null ">
   and Sa_UserFormTheme.LogoPosition like concat('%', #{SearchPojo.logoposition}, '%')
</if>
<if test="SearchPojo.backcolor != null ">
   and Sa_UserFormTheme.BackColor like concat('%', #{SearchPojo.backcolor}, '%')
</if>
<if test="SearchPojo.backimg != null ">
   and Sa_UserFormTheme.BackImg like concat('%', #{SearchPojo.backimg}, '%')
</if>
<if test="SearchPojo.themecolor != null ">
   and Sa_UserFormTheme.ThemeColor like concat('%', #{SearchPojo.themecolor}, '%')
</if>
<if test="SearchPojo.headimgurl != null ">
   and Sa_UserFormTheme.HeadImgUrl like concat('%', #{SearchPojo.headimgurl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_UserFormTheme.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_UserFormTheme.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_UserFormTheme.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_UserFormTheme.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_UserFormTheme.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_UserFormTheme.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_UserFormTheme.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_UserFormTheme.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_UserFormTheme.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_UserFormTheme.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.formid != null ">
   or Sa_UserFormTheme.FormId like concat('%', #{SearchPojo.formid}, '%')
</if>
<if test="SearchPojo.submitbtntext != null ">
   or Sa_UserFormTheme.SubmitBtnText like concat('%', #{SearchPojo.submitbtntext}, '%')
</if>
<if test="SearchPojo.logoimg != null ">
   or Sa_UserFormTheme.LogoImg like concat('%', #{SearchPojo.logoimg}, '%')
</if>
<if test="SearchPojo.logoposition != null ">
   or Sa_UserFormTheme.LogoPosition like concat('%', #{SearchPojo.logoposition}, '%')
</if>
<if test="SearchPojo.backcolor != null ">
   or Sa_UserFormTheme.BackColor like concat('%', #{SearchPojo.backcolor}, '%')
</if>
<if test="SearchPojo.backimg != null ">
   or Sa_UserFormTheme.BackImg like concat('%', #{SearchPojo.backimg}, '%')
</if>
<if test="SearchPojo.themecolor != null ">
   or Sa_UserFormTheme.ThemeColor like concat('%', #{SearchPojo.themecolor}, '%')
</if>
<if test="SearchPojo.headimgurl != null ">
   or Sa_UserFormTheme.HeadImgUrl like concat('%', #{SearchPojo.headimgurl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_UserFormTheme.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_UserFormTheme.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_UserFormTheme.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_UserFormTheme.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_UserFormTheme.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_UserFormTheme.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_UserFormTheme.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_UserFormTheme.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_UserFormTheme.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_UserFormTheme.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_UserFormTheme(id, FormId, SubmitBtnText, LogoImg, LogoPosition, BackColor, BackImg, ShowTitle, ShowDesc, ThemeColor, ShowNumber, ShowSubmitBtn, HeadImgUrl, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{formid}, #{submitbtntext}, #{logoimg}, #{logoposition}, #{backcolor}, #{backimg}, #{showtitle}, #{showdesc}, #{themecolor}, #{shownumber}, #{showsubmitbtn}, #{headimgurl}, #{remark}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_UserFormTheme
        <set>
            <if test="formid != null ">
                FormId =#{formid},
            </if>
            <if test="submitbtntext != null ">
                SubmitBtnText =#{submitbtntext},
            </if>
            <if test="logoimg != null ">
                LogoImg =#{logoimg},
            </if>
            <if test="logoposition != null ">
                LogoPosition =#{logoposition},
            </if>
            <if test="backcolor != null ">
                BackColor =#{backcolor},
            </if>
            <if test="backimg != null ">
                BackImg =#{backimg},
            </if>
            <if test="showtitle != null">
                ShowTitle =#{showtitle},
            </if>
            <if test="showdesc != null">
                ShowDesc =#{showdesc},
            </if>
            <if test="themecolor != null ">
                ThemeColor =#{themecolor},
            </if>
            <if test="shownumber != null">
                ShowNumber =#{shownumber},
            </if>
            <if test="showsubmitbtn != null">
                ShowSubmitBtn =#{showsubmitbtn},
            </if>
            <if test="headimgurl != null ">
                HeadImgUrl =#{headimgurl},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_UserFormTheme where id = #{key} 
    </delete>
                                                                                                            </mapper>

