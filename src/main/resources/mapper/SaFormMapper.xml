<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.edt.mapper.SaFormMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.edt.domain.pojo.SaFormPojo">
        select id,
               SourceId,
               SourceType,
               Name,
               Description,
               UserId,
               FormType,
               Status,
               IsDeleted,
               IsFolder,
               FolderId,
               Remark,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
        Deptid,
               Tenantid,
               Revision
        from Sa_Form
        where Sa_Form.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select id,
               SourceId,
               SourceType,
               Name,
               Description,
               UserId,
               FormType,
               Status,
               IsDeleted,
               IsFolder,
               FolderId,
               Remark,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Deptid,
               Tenantid,
               Revision
        from Sa_Form
    </sql>
    <sql id="selectdetailVo">
        select id,
               SourceId,
               SourceType,
               Name,
               Description,
               UserId,
               FormType,
               Status,
               IsDeleted,
               IsFolder,
               FolderId,
               Remark,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Deptid,
               Tenantid,
               Revision
        from Sa_Form
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.edt.domain.pojo.SaFormitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}

        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Form.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.sourceid != null">
            and Sa_Form.sourceid like concat('%', #{SearchPojo.sourceid}, '%')
        </if>
        <if test="SearchPojo.name != null">
            and Sa_Form.name like concat('%',
                #{SearchPojo.name}, '%')
        </if>
        <if test="SearchPojo.description != null">
            and Sa_Form.description like concat('%',
                #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.userid != null">
            and Sa_Form.userid like concat('%',
                #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.formtype != null">
            and Sa_Form.formtype like concat('%',
                #{SearchPojo.formtype}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Form.remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Form.createbyid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Form.createby like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Form.listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Form.lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Form.custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Form.custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Form.custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Form.custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Form.custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.sourceid != null">
                or Sa_Form.SourceId like concat('%', #{SearchPojo.sourceid}, '%')
            </if>
            <if test="SearchPojo.name != null">
                or Sa_Form.Name like concat('%', #{SearchPojo.name}, '%')
            </if>
            <if test="SearchPojo.description != null">
                or Sa_Form.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.userid != null">
                or Sa_Form.UserId like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.formtype != null">
                or Sa_Form.FormType like concat('%', #{SearchPojo.formtype}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Form.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Form.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Form.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Form.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Form.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Form.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Form.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Form.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Form.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Form.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.edt.domain.pojo.SaFormPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Form.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.sourceid != null">
            and Sa_Form.SourceId like concat('%', #{SearchPojo.sourceid}, '%')
        </if>
        <if test="SearchPojo.name != null">
            and Sa_Form.Name like concat('%',
                #{SearchPojo.name}, '%')
        </if>
        <if test="SearchPojo.description != null">
            and Sa_Form.Description like concat('%',
                #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.userid != null">
            and Sa_Form.UserId like concat('%',
                #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.formtype != null">
            and Sa_Form.FormType like concat('%',
                #{SearchPojo.formtype}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Form.Remark like concat('%',
                #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Form.CreateByid like concat('%',
                #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Form.CreateBy like concat('%',
                #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Form.Listerid like concat('%',
                #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Form.Lister like concat('%',
                #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Form.Custom1 like concat('%',
                #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Form.Custom2 like concat('%',
                #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Form.Custom3 like concat('%',
                #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Form.Custom4 like concat('%',
                #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Form.Custom5 like concat('%',
                #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.sourceid != null">
                or Sa_Form.SourceId like concat('%', #{SearchPojo.sourceid}, '%')
            </if>
            <if test="SearchPojo.name != null">
                or Sa_Form.Name like concat('%', #{SearchPojo.name}, '%')
            </if>
            <if test="SearchPojo.description != null">
                or Sa_Form.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.userid != null">
                or Sa_Form.UserId like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.formtype != null">
                or Sa_Form.FormType like concat('%', #{SearchPojo.formtype}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Form.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Form.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Form.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Form.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Form.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Form.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Form.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Form.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Form.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Form.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Form(id, SourceId, SourceType, Name, Description, UserId, FormType, Status, IsDeleted, IsFolder,
                            FolderId, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1,
                            Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, Revision)
        values (#{id}, #{sourceid}, #{sourcetype}, #{name}, #{description}, #{userid}, #{formtype}, #{status},
                #{isdeleted}, #{isfolder}, #{folderid}, #{remark}, #{createbyid}, #{createby}, #{createdate},
                #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
                #{deptid},
                #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Form
        <set>
            <if test="sourceid != null">
                SourceId =#{sourceid},
            </if>
            <if test="sourcetype != null">
                SourceType =#{sourcetype},
            </if>
            <if test="name != null">
                Name =#{name},
            </if>
            <if test="description != null">
                Description =#{description},
            </if>
            <if test="userid != null">
                UserId =#{userid},
            </if>
            <if test="formtype != null">
                FormType =#{formtype},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="isdeleted != null">
                IsDeleted =#{isdeleted},
            </if>
            <if test="isfolder != null">
                IsFolder =#{isfolder},
            </if>
            <if test="folderid != null">
                FolderId =#{folderid},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Form
        where id = #{key}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.edt.domain.pojo.SaFormPojo">
        select id
        from Sa_FormItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="logicDelete" resultType="java.lang.Integer">
        update Sa_Form
        set IsDeleted = 1
        where id = #{key}
    </select>

    <select id="getFormName" resultType="java.lang.String">
        select Name
        from Sa_Form
        where id = #{formid}
    </select>

    <select id="countForm" resultType="int">
        select count(1)
        from Sa_Form
    </select>

    <select id="getFormItemMap" resultType="java.util.Map">
        select FormItemId, Label
        from Sa_FormItem
        where Pid = #{formid}
        order by Sort
    </select>
</mapper>

