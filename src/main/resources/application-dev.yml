server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  datasource:
    #MYsql连接字符串
    url: **************************************************************************************************************************************************
    # 威海公网
#    url: **************************************************************************************************************************************************
    username: root
    password: asd@123456
#   威海公网
#    password: asd@weihai
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
  flyway:
    enabled: false   # 禁用Spring Boot自动Flyway配置，使用自定义DataSourceHelper
    initsql: ${INIT_SQL:http://dev.inksyun.com:31080/utils/File/proxy/appsetup/inksedt_init.sql}
  web: #配置静态资源访问路径
    resources:
      static-locations: classpath:/
  mvc:
    view:
      suffix: .html


mybatis:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.edt.**.domain
  #配置打印SQL语句到控制台


inks:
  redisType: mysql
  user:
    wxscan-create: true    #微信公众号扫码可直接创建admin权限用户，默认false
  feign:
    GrfUrl: http://dev.inksyun.com:18801
    UtsUrl: http://192.168.99.96:10684
  # 调用oam公众号接口获取openid #内网测试号:http://192.168.99.96:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce
  tid: tid-inks-edt
