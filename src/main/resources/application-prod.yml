server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  datasource:
    #MYsql连接字符串
    url: jdbc:mysql://${DATABASE_SER:192.168.99.240:53308/inksedt}?useUnicode=true&characterEncoding=utf-8&allowMutilQueries=true&serverTimezone=Asia/Shanghai&useSSL=false
    username: ${DATABASE_USER:root}
    password: ${DATABASE_PD:asd@123456}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
  flyway:
    enabled: false   # 禁用Spring Boot自动Flyway配置，使用自定义DataSourceHelper
    initsql: ${INIT_SQL:http://dev.inksyun.com:31080/utils/File/proxy/appsetup/inksedt_init.sql}
  web: #配置静态资源访问路径
    resources:
      static-locations: classpath:/
  mvc:
    view:
      suffix: .html


mybatis:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.edt.**.domain
  #配置打印SQL语句到控制台


inks:
  license: ${LICENSE_KEY:}
  redisType: mysql
  user:
    wxscan-create: true    #微信公众号扫码可直接创建admin权限用户，默认false
  feign:
    GrfUrl: ${GRFURL:http://dev.inksyun.com:18801}
    UtsUrl: ${UTSURL:http://192.168.99.96:10684}
  # 调用oam公众号接口获取openid #内网测试号:http://192.168.99.96:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce
  #  {"sn":"36412b2a817049f2974379be52e449fa","code":"crmdev","dt":1735091299597,"gp":"本地","env":"dev","lt":1830268799000}
  tid: tid-inks-crm
