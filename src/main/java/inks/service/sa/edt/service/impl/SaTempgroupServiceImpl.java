package inks.service.sa.edt.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaTempgroupEntity;
import inks.service.sa.edt.domain.pojo.SaTempgroupPojo;
import inks.service.sa.edt.mapper.SaTempgroupMapper;
import inks.service.sa.edt.service.SaTempgroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 项目模板分类(SaTempgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:45
 */
@Service("saTempgroupService")
public class SaTempgroupServiceImpl implements SaTempgroupService {
    @Resource
    private SaTempgroupMapper saTempgroupMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTempgroupPojo getEntity(String key) {
        return this.saTempgroupMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTempgroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTempgroupPojo> lst = saTempgroupMapper.getPageList(queryParam);
            PageInfo<SaTempgroupPojo> pageInfo = new PageInfo<SaTempgroupPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saTempgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTempgroupPojo insert(SaTempgroupPojo saTempgroupPojo) {
    //初始化NULL字段
     if(saTempgroupPojo.getGroupname()==null) saTempgroupPojo.setGroupname("");
     if(saTempgroupPojo.getRownum()==null) saTempgroupPojo.setRownum(0);
     if(saTempgroupPojo.getRemark()==null) saTempgroupPojo.setRemark("");
     if(saTempgroupPojo.getCreatebyid()==null) saTempgroupPojo.setCreatebyid("");
     if(saTempgroupPojo.getCreateby()==null) saTempgroupPojo.setCreateby("");
     if(saTempgroupPojo.getCreatedate()==null) saTempgroupPojo.setCreatedate(new Date());
     if(saTempgroupPojo.getListerid()==null) saTempgroupPojo.setListerid("");
     if(saTempgroupPojo.getLister()==null) saTempgroupPojo.setLister("");
     if(saTempgroupPojo.getModifydate()==null) saTempgroupPojo.setModifydate(new Date());
     if(saTempgroupPojo.getCustom1()==null) saTempgroupPojo.setCustom1("");
     if(saTempgroupPojo.getCustom2()==null) saTempgroupPojo.setCustom2("");
     if(saTempgroupPojo.getCustom3()==null) saTempgroupPojo.setCustom3("");
     if(saTempgroupPojo.getCustom4()==null) saTempgroupPojo.setCustom4("");
     if(saTempgroupPojo.getCustom5()==null) saTempgroupPojo.setCustom5("");
     if(saTempgroupPojo.getTenantid()==null) saTempgroupPojo.setTenantid("");
     if(saTempgroupPojo.getRevision()==null) saTempgroupPojo.setRevision(0);
        SaTempgroupEntity saTempgroupEntity = new SaTempgroupEntity(); 
        BeanUtils.copyProperties(saTempgroupPojo,saTempgroupEntity);
        //生成雪花id
          saTempgroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saTempgroupEntity.setRevision(1);  //乐观锁
          this.saTempgroupMapper.insert(saTempgroupEntity);
        return this.getEntity(saTempgroupEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saTempgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTempgroupPojo update(SaTempgroupPojo saTempgroupPojo) {
        SaTempgroupEntity saTempgroupEntity = new SaTempgroupEntity(); 
        BeanUtils.copyProperties(saTempgroupPojo,saTempgroupEntity);
        this.saTempgroupMapper.update(saTempgroupEntity);
        return this.getEntity(saTempgroupEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saTempgroupMapper.delete(key) ;
    }
    
                                                                                         
}
