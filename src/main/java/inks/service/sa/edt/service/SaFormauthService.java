package inks.service.sa.edt.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaFormPojo;
import inks.service.sa.edt.domain.pojo.SaFormauthPojo;
import inks.service.sa.edt.domain.pojo.SaFormdataPojo;
import inks.service.sa.edt.domain.vo.FormAuthVO;

import java.util.List;

/**
 * 表单授权对象(SaFormauth)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-02 16:48:40
 */
public interface SaFormauthService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormauthPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormauthPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFormauthPojo 实例对象
     * @return 实例对象
     */
    SaFormauthPojo insert(SaFormauthPojo saFormauthPojo);

    /**
     * 修改数据
     *
     * @param saFormauthpojo 实例对象
     * @return 实例对象
     */
    SaFormauthPojo update(SaFormauthPojo saFormauthpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<FormAuthVO> getAuthUsersAndDepts(String formid);

    List<String> getAllFormIdByUserid(String userid);

    List<SaFormPojo> getAllFormByUserid(String userid);

    List<SaFormPojo> getUnWriteFormByUserid(String userid);

    PageInfo<SaFormdataPojo> getSomeFormByUserid(QueryParam queryParam,String userid, String type);

    int countAllUsersUnWrite();
}
