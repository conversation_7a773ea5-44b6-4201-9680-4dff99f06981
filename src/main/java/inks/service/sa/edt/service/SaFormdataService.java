package inks.service.sa.edt.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaFormdataPojo;

import java.util.List;
import java.util.Map;

/**
 * 表单收集数据结果(SaFormdata)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-06 10:57:53
 */
public interface SaFormdataService {


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormdataPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormdataPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFormdataPojo 实例对象
     * @return 实例对象
     */
    SaFormdataPojo insert(SaFormdataPojo saFormdataPojo);

    /**
     * 修改数据
     *
     * @param saFormdatapojo 实例对象
     * @return 实例对象
     */
    SaFormdataPojo update(SaFormdataPojo saFormdatapojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                     /**
     * 审核数据
     *
     * @param saFormdataPojo 实例对象
     * @return 实例对象
     */
     SaFormdataPojo approval(SaFormdataPojo saFormdataPojo);

    List<Map<String, Object>> formSubmitNumberByDay(QueryParam queryParam);

    List<Map<String, Object>> formSubmitNumberByForm();
}
