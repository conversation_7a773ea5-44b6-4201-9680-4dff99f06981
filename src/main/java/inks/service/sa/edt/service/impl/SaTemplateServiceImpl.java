package inks.service.sa.edt.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaTemplateEntity;
import inks.service.sa.edt.domain.pojo.SaTemplatePojo;
import inks.service.sa.edt.mapper.SaTemplateMapper;
import inks.service.sa.edt.service.SaTemplateService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 表单模板(SaTemplate)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:28
 */
@Service("saTemplateService")
public class SaTemplateServiceImpl implements SaTemplateService {
    @Resource
    private SaTemplateMapper saTemplateMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTemplatePojo getEntity(String key) {
        return this.saTemplateMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTemplatePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTemplatePojo> lst = saTemplateMapper.getPageList(queryParam);
            PageInfo<SaTemplatePojo> pageInfo = new PageInfo<SaTemplatePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saTemplatePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTemplatePojo insert(SaTemplatePojo saTemplatePojo) {
    //初始化NULL字段
     if(saTemplatePojo.getFormid()==null) saTemplatePojo.setFormid("");
     if(saTemplatePojo.getCoverimg()==null) saTemplatePojo.setCoverimg("");
     if(saTemplatePojo.getName()==null) saTemplatePojo.setName("");
     if(saTemplatePojo.getDescription()==null) saTemplatePojo.setDescription("");
     if(saTemplatePojo.getGroupid()==null) saTemplatePojo.setGroupid(0);
     if(saTemplatePojo.getScheme()==null) saTemplatePojo.setScheme("{}");
     if(saTemplatePojo.getStatus()==null) saTemplatePojo.setStatus(0);
     if(saTemplatePojo.getRemark()==null) saTemplatePojo.setRemark("");
     if(saTemplatePojo.getCreatebyid()==null) saTemplatePojo.setCreatebyid("");
     if(saTemplatePojo.getCreateby()==null) saTemplatePojo.setCreateby("");
     if(saTemplatePojo.getCreatedate()==null) saTemplatePojo.setCreatedate(new Date());
     if(saTemplatePojo.getListerid()==null) saTemplatePojo.setListerid("");
     if(saTemplatePojo.getLister()==null) saTemplatePojo.setLister("");
     if(saTemplatePojo.getModifydate()==null) saTemplatePojo.setModifydate(new Date());
     if(saTemplatePojo.getCustom1()==null) saTemplatePojo.setCustom1("");
     if(saTemplatePojo.getCustom2()==null) saTemplatePojo.setCustom2("");
     if(saTemplatePojo.getCustom3()==null) saTemplatePojo.setCustom3("");
     if(saTemplatePojo.getCustom4()==null) saTemplatePojo.setCustom4("");
     if(saTemplatePojo.getCustom5()==null) saTemplatePojo.setCustom5("");
     if(saTemplatePojo.getTenantid()==null) saTemplatePojo.setTenantid("");
     if(saTemplatePojo.getRevision()==null) saTemplatePojo.setRevision(0);
        SaTemplateEntity saTemplateEntity = new SaTemplateEntity(); 
        BeanUtils.copyProperties(saTemplatePojo,saTemplateEntity);
        //生成雪花id
          saTemplateEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saTemplateEntity.setRevision(1);  //乐观锁
          this.saTemplateMapper.insert(saTemplateEntity);
        return this.getEntity(saTemplateEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saTemplatePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTemplatePojo update(SaTemplatePojo saTemplatePojo) {
        SaTemplateEntity saTemplateEntity = new SaTemplateEntity(); 
        BeanUtils.copyProperties(saTemplatePojo,saTemplateEntity);
        this.saTemplateMapper.update(saTemplateEntity);
        return this.getEntity(saTemplateEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saTemplateMapper.delete(key) ;
    }
    
                                                                                                                  
}
