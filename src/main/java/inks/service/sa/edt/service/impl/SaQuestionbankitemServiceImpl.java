package inks.service.sa.edt.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.edt.domain.SaQuestionbankitemEntity;
import inks.service.sa.edt.mapper.SaQuestionbankitemMapper;
import inks.service.sa.edt.service.SaQuestionbankitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 表单项(SaQuestionbankitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31 08:54:06
 */
@Service("saQuestionbankitemService")
public class SaQuestionbankitemServiceImpl implements SaQuestionbankitemService {
    @Resource
    private SaQuestionbankitemMapper saQuestionbankitemMapper;

    @Override
    public SaQuestionbankitemPojo getEntity(String key) {
        return this.saQuestionbankitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaQuestionbankitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuestionbankitemPojo> lst = saQuestionbankitemMapper.getPageList(queryParam);
            PageInfo<SaQuestionbankitemPojo> pageInfo = new PageInfo<SaQuestionbankitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaQuestionbankitemPojo> getList(String Pid) { 
        try {
            List<SaQuestionbankitemPojo> lst = saQuestionbankitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaQuestionbankitemPojo insert(SaQuestionbankitemPojo saQuestionbankitemPojo) {
        //初始化item的NULL
        SaQuestionbankitemPojo itempojo =this.clearNull(saQuestionbankitemPojo);
        SaQuestionbankitemEntity saQuestionbankitemEntity = new SaQuestionbankitemEntity(); 
        BeanUtils.copyProperties(itempojo,saQuestionbankitemEntity);
         //生成雪花id
          saQuestionbankitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saQuestionbankitemEntity.setRevision(1);  //乐观锁      
          this.saQuestionbankitemMapper.insert(saQuestionbankitemEntity);
        return this.getEntity(saQuestionbankitemEntity.getId());
  
    }

    @Override
    public SaQuestionbankitemPojo update(SaQuestionbankitemPojo saQuestionbankitemPojo) {
        SaQuestionbankitemEntity saQuestionbankitemEntity = new SaQuestionbankitemEntity(); 
        BeanUtils.copyProperties(saQuestionbankitemPojo,saQuestionbankitemEntity);
        this.saQuestionbankitemMapper.update(saQuestionbankitemEntity);
        return this.getEntity(saQuestionbankitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saQuestionbankitemMapper.delete(key) ;
    }

     @Override
     public SaQuestionbankitemPojo clearNull(SaQuestionbankitemPojo saQuestionbankitemPojo){
     //初始化NULL字段
     if(saQuestionbankitemPojo.getPid()==null) saQuestionbankitemPojo.setPid("");
     if(saQuestionbankitemPojo.getFormitemid()==null) saQuestionbankitemPojo.setFormitemid("");
     if(saQuestionbankitemPojo.getType()==null) saQuestionbankitemPojo.setType("");
     if(saQuestionbankitemPojo.getLabel()==null) saQuestionbankitemPojo.setLabel("");
     if(saQuestionbankitemPojo.getTextlabel()==null) saQuestionbankitemPojo.setTextlabel("");
     if(saQuestionbankitemPojo.getIsdisplaytype()==null) saQuestionbankitemPojo.setIsdisplaytype(0);
     if(saQuestionbankitemPojo.getIshidetype()==null) saQuestionbankitemPojo.setIshidetype(0);
     if(saQuestionbankitemPojo.getIsspecialtype()==null) saQuestionbankitemPojo.setIsspecialtype(0);
     if(saQuestionbankitemPojo.getShowlabel()==null) saQuestionbankitemPojo.setShowlabel(0);
     if(saQuestionbankitemPojo.getDefaultvalue()==null) saQuestionbankitemPojo.setDefaultvalue("");
     if(saQuestionbankitemPojo.getRequired()==null) saQuestionbankitemPojo.setRequired(0);
     if(saQuestionbankitemPojo.getPlaceholder()==null) saQuestionbankitemPojo.setPlaceholder("");
     if(saQuestionbankitemPojo.getSort()==null) saQuestionbankitemPojo.setSort(0);
     if(saQuestionbankitemPojo.getSpan()==null) saQuestionbankitemPojo.setSpan(0);
     if(saQuestionbankitemPojo.getScheme()==null) saQuestionbankitemPojo.setScheme("");
     if(saQuestionbankitemPojo.getReglist()==null) saQuestionbankitemPojo.setReglist("");
     if(saQuestionbankitemPojo.getShowcolumn()==null) saQuestionbankitemPojo.setShowcolumn(0);
     if(saQuestionbankitemPojo.getCustom1()==null) saQuestionbankitemPojo.setCustom1("");
     if(saQuestionbankitemPojo.getCustom2()==null) saQuestionbankitemPojo.setCustom2("");
     if(saQuestionbankitemPojo.getCustom3()==null) saQuestionbankitemPojo.setCustom3("");
     if(saQuestionbankitemPojo.getCustom4()==null) saQuestionbankitemPojo.setCustom4("");
     if(saQuestionbankitemPojo.getCustom5()==null) saQuestionbankitemPojo.setCustom5("");
     if(saQuestionbankitemPojo.getTenantid()==null) saQuestionbankitemPojo.setTenantid("");
     if(saQuestionbankitemPojo.getRevision()==null) saQuestionbankitemPojo.setRevision(0);
     return saQuestionbankitemPojo;
     }

    @Override
    public SaQuestionbankitemPojo getByFormItemId(String formItemId, String formKey) {
        return this.saQuestionbankitemMapper.getByFormItemId(formItemId, formKey);
    }
}
