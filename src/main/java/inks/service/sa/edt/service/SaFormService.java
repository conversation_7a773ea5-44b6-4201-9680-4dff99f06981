package inks.service.sa.edt.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaFormPojo;
import inks.service.sa.edt.domain.pojo.SaFormitemdetailPojo;
import inks.service.sa.edt.domain.vo.UserFormDetailVO;

import java.util.List;
import java.util.Map;

/**
 * 用户表单(SaForm)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-11 14:12:56
 */
public interface SaFormService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormitemdetailPojo> getPageList(QueryParam queryParam);

 /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormPojo> getBillList(QueryParam queryParam);
    
        /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormPojo> getPageTh(QueryParam queryParam);
    
    /**
     * 新增数据
     *
     * @param saFormPojo 实例对象
     * @return 实例对象
     */
    SaFormPojo insert(SaFormPojo saFormPojo);

    /**
     * 修改数据
     *
     * @param saFormpojo 实例对象
     * @return 实例对象
     */
    SaFormPojo update(SaFormPojo saFormpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    Integer logicDelete(String key);

    UserFormDetailVO details(String key);

    List<Map<String,Object>> getFormItemMap(String key);


//    List<Map<String,String>> getFormItemMap(String key);
}
