package inks.service.sa.edt.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaFormsettingEntity;
import inks.service.sa.edt.domain.constant.CommonConstants;
import inks.service.sa.edt.domain.pojo.SaFormPojo;
import inks.service.sa.edt.domain.pojo.SaFormsettingPojo;
import inks.service.sa.edt.domain.vo.FormSettingSchemaStruct;
import inks.service.sa.edt.mapper.SaFormMapper;
import inks.service.sa.edt.mapper.SaFormdataMapper;
import inks.service.sa.edt.mapper.SaFormsettingMapper;
import inks.service.sa.edt.service.SaFormsettingService;
import inks.sa.common.core.service.SaRedisService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 表单设置表(SaFormsetting)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:43
 */
@Service("saFormsettingService")
public class SaFormsettingServiceImpl implements SaFormsettingService {
    @Resource
    private SaFormsettingMapper saFormsettingMapper;
    @Resource
    private SaFormMapper saFormMapper;
    @Resource
    private SaFormdataMapper saFormdataMapper;
    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormsettingPojo getEntity(String key) {
        return this.saFormsettingMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormsettingPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormsettingPojo> lst = saFormsettingMapper.getPageList(queryParam);
            PageInfo<SaFormsettingPojo> pageInfo = new PageInfo<SaFormsettingPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saFormsettingPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormsettingPojo insert(SaFormsettingPojo saFormsettingPojo) {
        //初始化NULL字段
        if (saFormsettingPojo.getFormid() == null) saFormsettingPojo.setFormid("");
        if (saFormsettingPojo.getSettings() == null) saFormsettingPojo.setSettings("{}");
        if (saFormsettingPojo.getRemark() == null) saFormsettingPojo.setRemark("");
        if (saFormsettingPojo.getCreatebyid() == null) saFormsettingPojo.setCreatebyid("");
        if (saFormsettingPojo.getCreateby() == null) saFormsettingPojo.setCreateby("");
        if (saFormsettingPojo.getCreatedate() == null) saFormsettingPojo.setCreatedate(new Date());
        if (saFormsettingPojo.getListerid() == null) saFormsettingPojo.setListerid("");
        if (saFormsettingPojo.getLister() == null) saFormsettingPojo.setLister("");
        if (saFormsettingPojo.getModifydate() == null) saFormsettingPojo.setModifydate(new Date());
        if (saFormsettingPojo.getCustom1() == null) saFormsettingPojo.setCustom1("");
        if (saFormsettingPojo.getCustom2() == null) saFormsettingPojo.setCustom2("");
        if (saFormsettingPojo.getCustom3() == null) saFormsettingPojo.setCustom3("");
        if (saFormsettingPojo.getCustom4() == null) saFormsettingPojo.setCustom4("");
        if (saFormsettingPojo.getCustom5() == null) saFormsettingPojo.setCustom5("");
        if (saFormsettingPojo.getTenantid() == null) saFormsettingPojo.setTenantid("");
        if (saFormsettingPojo.getRevision() == null) saFormsettingPojo.setRevision(0);
        SaFormsettingEntity saFormsettingEntity = new SaFormsettingEntity();
        BeanUtils.copyProperties(saFormsettingPojo, saFormsettingEntity);
        //生成雪花id
        saFormsettingEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saFormsettingEntity.setRevision(1);  //乐观锁
        this.saFormsettingMapper.insert(saFormsettingEntity);
        return this.getEntity(saFormsettingEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saFormsettingPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormsettingPojo update(SaFormsettingPojo saFormsettingPojo) {
        SaFormsettingEntity saFormsettingEntity = new SaFormsettingEntity();
        BeanUtils.copyProperties(saFormsettingPojo, saFormsettingEntity);
        this.saFormsettingMapper.update(saFormsettingEntity);
        return this.getEntity(saFormsettingEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormsettingMapper.delete(key);
    }

    @Override
    public R<Boolean> getUserFormWriteSettingStatus(String formId, String ipAddr, String wxOpenId, Integer type) {
        SaFormPojo userFormEntity = saFormMapper.getEntity(formId);
        boolean checkPublish = Objects.equals(type, CommonConstants.ConstantNumber.ONE) &&
                (ObjectUtil.isNull(userFormEntity) || userFormEntity.getStatus() != 2);//状态:1未发布2收集中3停止发布
        // 非公开填写 不校验发布状态
        if (checkPublish) {
            return R.ok(null, "表单暂时无法填写");
        }
        SaFormsettingPojo settingEntity = saFormsettingMapper.getEntityByFormId(formId);
        if (ObjectUtil.isNull(settingEntity)) {
            return R.ok(true);
        }
        FormSettingSchemaStruct settingSchemaStruct = BeanUtil.toBean(settingEntity.getSettings(), FormSettingSchemaStruct.class);
        // 填写时间限制
        boolean writeInterviewTime = isWriteInterviewTime(settingSchemaStruct);
        if (!writeInterviewTime) {
            return R.ok(null, StrUtil.blankToDefault(settingSchemaStruct.getWriteInterviewTimeText(), "不在答题时间范围内，有问题请与表单发布者联系"));
        }
        // 每个微信答题次数限制
        if (settingSchemaStruct.isWxWriteCountLimitStatus()) {
            String rangeTypeSql = FormSettingSchemaStruct.DateRangeType.getDateSql(settingSchemaStruct.getWxWriteCountLimitDateType());
            // 通過formId、wxOpenId、rangeTypeSql查詢答題次數
            int writeCount = saFormdataMapper.getCountByFormIdWxAndIpAndUseridAndTime(formId, wxOpenId, null, null, rangeTypeSql);
            if (writeCount >= settingSchemaStruct.getWxWriteCountLimit()) {
                return R.ok(null, StrUtil.blankToDefault(settingSchemaStruct.getWxWriteCountLimitText(), "该微信已经提交过数据，不可重复提交，有问题请与表单发布者联系"));
            }
        }
        // 每个IP答题次数限制
        if (settingSchemaStruct.isIpWriteCountLimitStatus()) {
            String rangeTypeSql = FormSettingSchemaStruct.DateRangeType.getDateSql(settingSchemaStruct.getIpWriteCountLimitDateType());
            // 通過formId、ipAddr、rangeTypeSql查詢答題次數
            int writeCount = saFormdataMapper.getCountByFormIdWxAndIpAndUseridAndTime(formId, null, ipAddr, null, rangeTypeSql);
            if (writeCount >= settingSchemaStruct.getIpWriteCountLimit()) {
                return R.ok(null, StrUtil.blankToDefault(settingSchemaStruct.getIpWriteCountLimitText(), "该IP已经提交过数据，不可重复提交，有问题请与表单发布者联系"));
            }
        }
        // 总答题次数限制
        if (settingSchemaStruct.isTotalWriteCountLimitStatus()) {
            String rangeTypeSql = FormSettingSchemaStruct.DateRangeType.getDateSql(settingSchemaStruct.getTotalWriteCountLimitDateType());
            // 通過formId、rangeTypeSql查詢答題次數
            int writeCount = saFormdataMapper.getCountByFormIdWxAndIpAndUseridAndTime(formId, null, null, null, rangeTypeSql);
            if (writeCount >= settingSchemaStruct.getTotalWriteCountLimit()) {
                return R.ok(null, StrUtil.blankToDefault(settingSchemaStruct.getTotalWriteCountLimitText(), "该表单收集数据已经达到上限，有问题请与表单发布者联系"));
            }
        }
        // 每个账号答题次数限制
        if (settingSchemaStruct.isAccountWriteCountLimitStatus()) {
            String userid = saRedisService.getLoginUser().getUserid();
            String rangeTypeSql = FormSettingSchemaStruct.DateRangeType.getDateSql(settingSchemaStruct.getAccountWriteCountLimitDateType());
            int writeCount = saFormdataMapper.getCountByFormIdWxAndIpAndUseridAndTime(formId, null, null, userid, rangeTypeSql);
            if (writeCount >= settingSchemaStruct.getAccountWriteCountLimit()) {
                return R.ok(null, StrUtil.blankToDefault(settingSchemaStruct.getAccountWriteCountLimitText(), "该账号已经提交过数据，不可重复提交，有问题请与表单发布者联系"));
            }
        }
        return R.ok(true);
    }


    /**
     * 是否在设置的答题时间内
     *
     * @return true 在答题时间内
     */
    private boolean isWriteInterviewTime(FormSettingSchemaStruct settingSchemaStruct) {
        // 答题时间限制
        if (settingSchemaStruct.isWriteInterviewTimeStatus()) {
            // 是否每天时间范围限制
            if (settingSchemaStruct.isWriteInterviewDayTimeStatus()) {
                // 是否在允许访问的天内
                List<String> writeInterviewDateRange = settingSchemaStruct.getWriteInterviewDateRange();
                if (CollUtil.isEmpty(writeInterviewDateRange) || DateUtil.isIn(DateUtil.date(), DateUtil.parse(writeInterviewDateRange.get(0)), DateUtil.parse(writeInterviewDateRange.get(1)))) {
                    // 是否在允许访问的小时内
                    List<String> writeInterviewTimeRange = settingSchemaStruct.getWriteInterviewTimeRange();
                    LocalTime now = LocalTime.now();
                    boolean isRange = CollUtil.isNotEmpty(writeInterviewDateRange) && now.isBefore(LocalTime.parse(writeInterviewTimeRange.get(0))) || now.isAfter(LocalTime.parse(writeInterviewTimeRange.get(1)));
                    if (isRange) {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                // 是否在允许访问的天内
                List<String> writeInterviewDateTimeRange = settingSchemaStruct.getWriteInterviewDateTimeRange();
                if (CollUtil.isNotEmpty(writeInterviewDateTimeRange) && !DateUtil.isIn(DateUtil.date(), DateUtil.parse(writeInterviewDateTimeRange.get(0)), DateUtil.parse(writeInterviewDateTimeRange.get(1)))) {
                    return false;
                }
            }
            // 是否是每周允许访问的周几
            List<String> writeInterviewTimeWhichDays = settingSchemaStruct.getWriteInterviewTimeWhichDays();
            if (CollUtil.isNotEmpty(writeInterviewTimeWhichDays)) {
                // 获取今天是每周的第几天
                int day = DateUtil.dayOfWeek(DateUtil.date());
                return writeInterviewTimeWhichDays.contains(String.valueOf(day));
            }
        }
        return true;
    }
}
