package inks.service.sa.edt.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaFormthemeEntity;
import inks.service.sa.edt.domain.pojo.SaFormthemePojo;
import inks.service.sa.edt.mapper.SaFormthemeMapper;
import inks.service.sa.edt.service.SaFormthemeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 项目主题外观模板(SaFormtheme)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:43
 */
@Service("saFormthemeService")
public class SaFormthemeServiceImpl implements SaFormthemeService {
    @Resource
    private SaFormthemeMapper saFormthemeMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormthemePojo getEntity(String key) {
        return this.saFormthemeMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormthemePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormthemePojo> lst = saFormthemeMapper.getPageList(queryParam);
            PageInfo<SaFormthemePojo> pageInfo = new PageInfo<SaFormthemePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saFormthemePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormthemePojo insert(SaFormthemePojo saFormthemePojo) {
    //初始化NULL字段
     if(saFormthemePojo.getName()==null) saFormthemePojo.setName("");
     if(saFormthemePojo.getStyle()==null) saFormthemePojo.setStyle(0);
     if(saFormthemePojo.getHeadimgurl()==null) saFormthemePojo.setHeadimgurl("");
     if(saFormthemePojo.getBackgroundimg()==null) saFormthemePojo.setBackgroundimg("");
     if(saFormthemePojo.getThemecolor()==null) saFormthemePojo.setThemecolor("");
     if(saFormthemePojo.getRemark()==null) saFormthemePojo.setRemark("");
     if(saFormthemePojo.getCreatebyid()==null) saFormthemePojo.setCreatebyid("");
     if(saFormthemePojo.getCreateby()==null) saFormthemePojo.setCreateby("");
     if(saFormthemePojo.getCreatedate()==null) saFormthemePojo.setCreatedate(new Date());
     if(saFormthemePojo.getListerid()==null) saFormthemePojo.setListerid("");
     if(saFormthemePojo.getLister()==null) saFormthemePojo.setLister("");
     if(saFormthemePojo.getModifydate()==null) saFormthemePojo.setModifydate(new Date());
     if(saFormthemePojo.getCustom1()==null) saFormthemePojo.setCustom1("");
     if(saFormthemePojo.getCustom2()==null) saFormthemePojo.setCustom2("");
     if(saFormthemePojo.getCustom3()==null) saFormthemePojo.setCustom3("");
     if(saFormthemePojo.getCustom4()==null) saFormthemePojo.setCustom4("");
     if(saFormthemePojo.getCustom5()==null) saFormthemePojo.setCustom5("");
     if(saFormthemePojo.getTenantid()==null) saFormthemePojo.setTenantid("");
     if(saFormthemePojo.getRevision()==null) saFormthemePojo.setRevision(0);
        SaFormthemeEntity saFormthemeEntity = new SaFormthemeEntity(); 
        BeanUtils.copyProperties(saFormthemePojo,saFormthemeEntity);
        //生成雪花id
          saFormthemeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFormthemeEntity.setRevision(1);  //乐观锁
          this.saFormthemeMapper.insert(saFormthemeEntity);
        return this.getEntity(saFormthemeEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFormthemePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormthemePojo update(SaFormthemePojo saFormthemePojo) {
        SaFormthemeEntity saFormthemeEntity = new SaFormthemeEntity(); 
        BeanUtils.copyProperties(saFormthemePojo,saFormthemeEntity);
        this.saFormthemeMapper.update(saFormthemeEntity);
        return this.getEntity(saFormthemeEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormthemeMapper.delete(key) ;
    }
    
                                                                                                        
}
