package inks.service.sa.edt.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaQuestionbankPojo;
import inks.service.sa.edt.domain.pojo.SaQuestionbankitemdetailPojo;
import inks.service.sa.edt.domain.vo.UserQuestionbankDetailVO;
import com.github.pagehelper.PageInfo;

/**
 * 用户表单(SaQuestionbank)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-31 08:53:52
 */
public interface SaQuestionbankService {


    SaQuestionbankPojo getEntity(String key);

    PageInfo<SaQuestionbankitemdetailPojo> getPageList(QueryParam queryParam);

    SaQuestionbankPojo getBillEntity(String key);

    PageInfo<SaQuestionbankPojo> getBillList(QueryParam queryParam);

    PageInfo<SaQuestionbankPojo> getPageTh(QueryParam queryParam);

    SaQuestionbankPojo insert(SaQuestionbankPojo saQuestionbankPojo);

    SaQuestionbankPojo update(SaQuestionbankPojo saQuestionbankpojo);

    int delete(String key);

    UserQuestionbankDetailVO details(String key);
}
