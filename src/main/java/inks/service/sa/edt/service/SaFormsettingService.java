package inks.service.sa.edt.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.service.sa.edt.domain.pojo.SaFormsettingPojo;

/**
 * 表单设置表(SaFormsetting)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:43
 */
public interface SaFormsettingService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormsettingPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormsettingPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFormsettingPojo 实例对象
     * @return 实例对象
     */
    SaFormsettingPojo insert(SaFormsettingPojo saFormsettingPojo);

    /**
     * 修改数据
     *
     * @param saFormsettingpojo 实例对象
     * @return 实例对象
     */
    SaFormsettingPojo update(SaFormsettingPojo saFormsettingpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    R<Boolean> getUserFormWriteSettingStatus(String formId, String ipAddr, String wxOpenId, Integer type);
}
