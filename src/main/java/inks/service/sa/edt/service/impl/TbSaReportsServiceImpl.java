package inks.service.sa.edt.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaReportsEntity;
import inks.service.sa.edt.domain.pojo.SaReportsPojo;
import inks.service.sa.edt.mapper.Tb_SaReportsMapper;
import inks.service.sa.edt.service.Tb_SaReportsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 报表中心(含Formid)(SaReports)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:49
 */
@Service("tbSaReportsService")
public class TbSaReportsServiceImpl implements Tb_SaReportsService {
    @Resource
    private Tb_SaReportsMapper tbSaReportsMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaReportsPojo getEntity(String key) {
        return this.tbSaReportsMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaReportsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReportsPojo> lst = tbSaReportsMapper.getPageList(queryParam);
            PageInfo<SaReportsPojo> pageInfo = new PageInfo<SaReportsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saReportsPojo 实例对象
     * @return 实例对象
     *      * boolean isConversion 是否进行转换(正常情况下需要进行转换(即true) 将前端传来的Rptdata BASE64转换成XML)
     *      * 但是当复制表单的打印模板时,不需要再进行转换了!因为读取的就是转换保存进DB的Rptdata
     */
    @Override
    public SaReportsPojo insert(SaReportsPojo saReportsPojo,boolean isConversion) {
        //初始化NULL字段
        if (saReportsPojo.getGengroupid() == null) saReportsPojo.setGengroupid("");
        if (saReportsPojo.getFormid() == null) saReportsPojo.setFormid("");
        if (saReportsPojo.getModulecode() == null) saReportsPojo.setModulecode("");
        if (saReportsPojo.getRpttype() == null) saReportsPojo.setRpttype("");
        if (saReportsPojo.getRptname() == null) saReportsPojo.setRptname("");
        if (saReportsPojo.getRptdata() == null) saReportsPojo.setRptdata("");
        if (saReportsPojo.getPagerow() == null) saReportsPojo.setPagerow(0);
        if (saReportsPojo.getTempurl() == null) saReportsPojo.setTempurl("");
        if (saReportsPojo.getFilename() == null) saReportsPojo.setFilename("");
        if (saReportsPojo.getPrintersn() == null) saReportsPojo.setPrintersn("");
        if (saReportsPojo.getRownum() == null) saReportsPojo.setRownum(0);
        if (saReportsPojo.getEnabledmark() == null) saReportsPojo.setEnabledmark(0);
        if (saReportsPojo.getGrfdata() == null) saReportsPojo.setGrfdata("");
        if (saReportsPojo.getPaperlength() == null) saReportsPojo.setPaperlength(0D);
        if (saReportsPojo.getPaperwidth() == null) saReportsPojo.setPaperwidth(0D);
        if (saReportsPojo.getRemark() == null) saReportsPojo.setRemark("");
        if (saReportsPojo.getCreateby() == null) saReportsPojo.setCreateby("");
        if (saReportsPojo.getCreatebyid() == null) saReportsPojo.setCreatebyid("");
        if (saReportsPojo.getCreatedate() == null) saReportsPojo.setCreatedate(new Date());
        if (saReportsPojo.getLister() == null) saReportsPojo.setLister("");
        if (saReportsPojo.getListerid() == null) saReportsPojo.setListerid("");
        if (saReportsPojo.getModifydate() == null) saReportsPojo.setModifydate(new Date());
        if (saReportsPojo.getCustom1() == null) saReportsPojo.setCustom1("");
        if (saReportsPojo.getCustom2() == null) saReportsPojo.setCustom2("");
        if (saReportsPojo.getCustom3() == null) saReportsPojo.setCustom3("");
        if (saReportsPojo.getCustom4() == null) saReportsPojo.setCustom4("");
        if (saReportsPojo.getCustom5() == null) saReportsPojo.setCustom5("");
        if (saReportsPojo.getTenantid() == null) saReportsPojo.setTenantid("");
        if (saReportsPojo.getTenantname() == null) saReportsPojo.setTenantname("");
        if (saReportsPojo.getRevision() == null) saReportsPojo.setRevision(0);
        SaReportsEntity saReportsEntity = new SaReportsEntity();
        BeanUtils.copyProperties(saReportsPojo, saReportsEntity);
//    boolean isConversion 是否进行转换(正常情况下需要进行转换(即true) 将前端传来的Rptdata BASE64转换成XML)
//    但是当复制表单的打印模板时,不需要再进行转换了!因为读取的就是转换保存进DB的Rptdata
        if (isConversion) {
            if (saReportsEntity.getRptdata() != null) {
                try {
                    //将前端传来的BASE64转换成XML
                    saReportsEntity.setRptdata(new String(inks.common.core.utils.Base64.decode(saReportsEntity.getRptdata()), "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
            if (saReportsEntity.getGrfdata() != null) {
                try {
                    //将前端传来的BASE64转换成XML
                    saReportsEntity.setGrfdata(new String(inks.common.core.utils.Base64.decode(saReportsEntity.getGrfdata()), "UTF-8"));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
        }

        saReportsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saReportsEntity.setRevision(1);  //乐观锁
        this.tbSaReportsMapper.insert(saReportsEntity);
        return this.getEntity(saReportsEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saReportsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReportsPojo update(SaReportsPojo saReportsPojo) {
        SaReportsEntity saReportsEntity = new SaReportsEntity();
        BeanUtils.copyProperties(saReportsPojo, saReportsEntity);
        if (saReportsEntity.getRptdata() != null) {
            try {
                //将前端传来的BASE64转换成XML
                saReportsEntity.setRptdata(new String(inks.common.core.utils.Base64.decode(saReportsEntity.getRptdata()), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        if (saReportsEntity.getGrfdata() != null) {
            try {
                //将前端传来的BASE64转换成XML
                saReportsEntity.setGrfdata(new String(inks.common.core.utils.Base64.decode(saReportsEntity.getGrfdata()), "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }
        this.tbSaReportsMapper.update(saReportsEntity);
        return this.getEntity(saReportsEntity.getId());
    }


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.tbSaReportsMapper.delete(key);
    }


    @Override
    public List<SaReportsPojo> pullDefault(String moduleCode, LoginUser loginUser) {
        try {
            //搜索默认科目模板
            List<SaReportsPojo> lstnew = new ArrayList<>();
            List<SaReportsPojo> lstdef = this.tbSaReportsMapper.getListByDef(moduleCode);
            for (SaReportsPojo item : lstdef) {
                SaReportsPojo dbpojo = tbSaReportsMapper.getEntityByNameCode(item.getRptname(), item.getModulecode());
                if (dbpojo == null) {
                    SaReportsPojo saReportsPojo = new SaReportsPojo();
                    BeanUtils.copyProperties(item, saReportsPojo);
                    saReportsPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
//                    saReportsPojo.setTenantid(loginUser.getTenantid());
//                    saReportsPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                    saReportsPojo.setCreatebyid(loginUser.getUserid());
                    saReportsPojo.setCreateby(loginUser.getUsername());
                    saReportsPojo.setCreatedate(new Date());
                    saReportsPojo.setListerid(loginUser.getUserid());
                    saReportsPojo.setLister(loginUser.getUsername());
                    saReportsPojo.setModifydate(new Date());
                    SaReportsEntity saReportsEntity = new SaReportsEntity();
                    BeanUtils.copyProperties(saReportsPojo, saReportsEntity);
                    this.tbSaReportsMapper.insert(saReportsEntity);
                    lstnew.add(saReportsPojo);
                }
            }
            return lstnew;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaReportsPojo> getPageListAll(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReportsPojo> lst = tbSaReportsMapper.getPageListAll(queryParam);
            PageInfo<SaReportsPojo> pageInfo = new PageInfo<SaReportsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<SaReportsPojo> getListByModuleCode(String moduleCode) {
        try {
            //自定义报表
            List<SaReportsPojo> lst = tbSaReportsMapper.getListByModuleCode(moduleCode);
            //默认格式
            // List<CireportsPojo> lstdef = cireportsMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            //lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaReportsPojo> getListByFormId(String formid,String code,QueryParam queryParam) {
        try {
            //自定义报表
            List<SaReportsPojo> lst = tbSaReportsMapper.getListByFormId(formid,code,queryParam);
            //默认格式
            // List<CireportsPojo> lstdef = cireportsMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            //lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
