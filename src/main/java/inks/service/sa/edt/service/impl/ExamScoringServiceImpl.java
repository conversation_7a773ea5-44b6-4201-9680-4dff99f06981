package inks.service.sa.edt.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.edt.domain.pojo.SaFormdataPojo;
import inks.service.sa.edt.domain.pojo.SaFormitemPojo;
import inks.service.sa.edt.domain.vo.ExamScoreResult;
import inks.service.sa.edt.service.ExamScoringService;
import inks.service.sa.edt.service.SaFormdataService;
import inks.service.sa.edt.service.SaFormitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 考试评分服务实现类
 * 复用现有的JSON处理逻辑和scheme解析功能
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
public class ExamScoringServiceImpl implements ExamScoringService {

    @Resource
    private SaFormitemService saFormitemService;

    @Resource
    private SaFormdataService saFormdataService;

    @Override
    public ExamScoreResult calculateScore(String formId, Map<String, Object> answers) {
        ExamScoreResult result = new ExamScoreResult();
        
        try {
            // 获取表单的所有题目
            List<SaFormitemPojo> formItems = saFormitemService.getList(formId);
            if (formItems == null || formItems.isEmpty()) {
                throw new BaseBusinessException("表单题目为空");
            }

            // 初始化评分结果
            BigDecimal totalScore = BigDecimal.ZERO;
            BigDecimal fullScore = BigDecimal.ZERO;
            BigDecimal objectiveScore = BigDecimal.ZERO;
            BigDecimal subjectiveScore = BigDecimal.ZERO;
            
            Map<String, BigDecimal> questionScores = new HashMap<>();
            List<ExamScoreResult.QuestionScoreDetail> questionDetails = new ArrayList<>();
            List<String> manualGradingQuestions = new ArrayList<>();
            
            boolean hasManualGrading = false;

            // 遍历每个题目进行评分
            for (SaFormitemPojo item : formItems) {
                String questionId = item.getFormitemid();
                String scheme = item.getScheme();
                
                if (scheme == null || scheme.trim().isEmpty()) {
                    continue; // 跳过没有scheme的项目
                }

                ExamScoreResult.QuestionScoreDetail detail = scoreQuestion(item, answers.get(questionId));
                questionDetails.add(detail);
                
                BigDecimal questionScore = detail.getScore();
                BigDecimal questionFullScore = detail.getFullScore();
                
                questionScores.put(questionId, questionScore);
                totalScore = totalScore.add(questionScore);
                fullScore = fullScore.add(questionFullScore);
                
                // 根据评分类型累计分数
                if (detail.getScoringType() == 1) { // 客观题
                    objectiveScore = objectiveScore.add(questionScore);
                } else if (detail.getScoringType() == 2 || detail.getScoringType() == 0) { // 主观题或手动评分
                    subjectiveScore = subjectiveScore.add(questionScore);
                    if (detail.getScoringType() == 2 || detail.getScoringType() == 0) {
                        manualGradingQuestions.add(questionId);
                        hasManualGrading = true;
                    }
                } else if (detail.getScoringType() == 5) { // 横向填空
                    objectiveScore = objectiveScore.add(questionScore);
                }
            }

            // 设置评分结果
            result.setTotalScore(totalScore);
            result.setFullScore(fullScore);
            result.setObjectiveScore(objectiveScore);
            result.setSubjectiveScore(subjectiveScore);
            result.setQuestionScores(questionScores);
            result.setQuestionDetails(questionDetails);
            result.setManualGradingQuestions(manualGradingQuestions);
            
            // 计算是否及格 (假设60分及格)
            if (fullScore.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal passRate = totalScore.divide(fullScore, 4, RoundingMode.HALF_UP);
                result.setIsPassed(passRate.compareTo(new BigDecimal("0.6")) >= 0 ? 1 : 0);
            } else {
                result.setIsPassed(0);
            }
            
            // 设置评分状态
            if (hasManualGrading) {
                result.setScoringStatus("待人工评分");
            } else {
                result.setScoringStatus("自动评分完成");
            }

        } catch (Exception e) {
            result.setErrorMessage("评分过程中发生错误: " + e.getMessage());
            result.setScoringStatus("评分失败");
        }

        return result;
    }

    @Override
    public ExamScoreResult recalculateScore(String formDataId) {
        try {
            SaFormdataPojo formData = saFormdataService.getEntity(formDataId);
            if (formData == null) {
                throw new BaseBusinessException("答题记录不存在");
            }

            String originalData = formData.getOriginaldata();
            Map<String, Object> answers = JSONArray.parseObject(originalData, Map.class);
            
            return calculateScore(formData.getFormid(), answers);
        } catch (Exception e) {
            ExamScoreResult result = new ExamScoreResult();
            result.setErrorMessage("重新计算分数失败: " + e.getMessage());
            result.setScoringStatus("评分失败");
            return result;
        }
    }

    @Override
    public Object getCorrectAnswer(String formId, String questionId) {
        try {
            List<SaFormitemPojo> formItems = saFormitemService.getList(formId);
            for (SaFormitemPojo item : formItems) {
                if (questionId.equals(item.getFormitemid())) {
                    JSONObject schemeJson = JSON.parseObject(item.getScheme());
                    JSONObject examConfig = schemeJson.getJSONObject("examConfig");
                    if (examConfig != null) {
                        return examConfig.get("answer");
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常，返回null
        }
        return null;
    }

    @Override
    public boolean validateExamConfig(String formId) {
        try {
            List<SaFormitemPojo> formItems = saFormitemService.getList(formId);
            for (SaFormitemPojo item : formItems) {
                String scheme = item.getScheme();
                if (scheme == null || scheme.trim().isEmpty()) {
                    continue;
                }
                
                JSONObject schemeJson = JSON.parseObject(scheme);
                JSONObject examConfig = schemeJson.getJSONObject("examConfig");
                
                if (examConfig == null) {
                    return false; // 缺少examConfig
                }
                
                // 检查必要字段
                if (!examConfig.containsKey("scoringType") || 
                    !examConfig.containsKey("score") || 
                    !examConfig.containsKey("enableScore")) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 对单个题目进行评分
     * 复用现有的ParseScheme逻辑
     */
    private ExamScoreResult.QuestionScoreDetail scoreQuestion(SaFormitemPojo item, Object userAnswer) {
        ExamScoreResult.QuestionScoreDetail detail = new ExamScoreResult.QuestionScoreDetail();
        detail.setQuestionId(item.getFormitemid());
        detail.setQuestionType(item.getType());
        detail.setUserAnswer(userAnswer);
        
        try {
            JSONObject schemeJson = JSON.parseObject(item.getScheme());
            JSONObject examConfig = schemeJson.getJSONObject("examConfig");
            
            if (examConfig == null) {
                detail.setScore(BigDecimal.ZERO);
                detail.setFullScore(BigDecimal.ZERO);
                detail.setScoringType(0);
                detail.setScoringNote("缺少examConfig配置");
                return detail;
            }
            
            Integer scoringType = examConfig.getInteger("scoringType");
            BigDecimal fullScore = examConfig.getBigDecimal("score");
            Object correctAnswer = examConfig.get("answer");
            
            detail.setScoringType(scoringType);
            detail.setFullScore(fullScore != null ? fullScore : BigDecimal.ZERO);
            detail.setCorrectAnswer(correctAnswer);
            
            // 根据评分类型进行评分
            BigDecimal score = scoreByType(scoringType, userAnswer, correctAnswer, fullScore, schemeJson);
            detail.setScore(score);
            detail.setIsCorrect(score.compareTo(detail.getFullScore()) == 0);
            
        } catch (Exception e) {
            detail.setScore(BigDecimal.ZERO);
            detail.setFullScore(BigDecimal.ZERO);
            detail.setScoringType(0);
            detail.setScoringNote("评分异常: " + e.getMessage());
        }
        
        return detail;
    }

    /**
     * 根据评分类型进行评分
     */
    private BigDecimal scoreByType(Integer scoringType, Object userAnswer, Object correctAnswer, 
                                   BigDecimal fullScore, JSONObject schemeJson) {
        if (userAnswer == null || fullScore == null) {
            return BigDecimal.ZERO;
        }
        
        switch (scoringType) {
            case 1: // 客观题自动评分 (RADIO, CHECKBOX, SELECT)
                return scoreObjectiveQuestion(userAnswer, correctAnswer, fullScore);
            case 2: // 主观题，标记为待人工评分 (INPUT, TEXTAREA)
                return BigDecimal.ZERO; // 主观题需要人工评分
            case 5: // 横向填空多点评分 (HORIZONTAL_INPUT)
                return scoreHorizontalInput(userAnswer, correctAnswer, schemeJson);
            case 0: // 手动评分 (SIGN_PAD)
            default:
                return BigDecimal.ZERO; // 需要人工评分
        }
    }

    /**
     * 客观题评分 (RADIO, CHECKBOX, SELECT)
     */
    private BigDecimal scoreObjectiveQuestion(Object userAnswer, Object correctAnswer, BigDecimal fullScore) {
        if (correctAnswer == null) {
            return BigDecimal.ZERO;
        }
        
        // 处理数组类型的答案 (多选题)
        if (correctAnswer instanceof List && userAnswer instanceof List) {
            List<?> correctList = (List<?>) correctAnswer;
            List<?> userList = (List<?>) userAnswer;
            
            // 转换为Set进行比较，忽略顺序
            Set<Object> correctSet = new HashSet<>(correctList);
            Set<Object> userSet = new HashSet<>(userList);
            
            return correctSet.equals(userSet) ? fullScore : BigDecimal.ZERO;
        }
        
        // 处理单个值的答案 (单选题)
        return Objects.equals(userAnswer, correctAnswer) ? fullScore : BigDecimal.ZERO;
    }

    /**
     * 横向填空评分 (HORIZONTAL_INPUT)
     * 支持多个填空点的分别评分
     */
    private BigDecimal scoreHorizontalInput(Object userAnswer, Object correctAnswer, JSONObject schemeJson) {
        try {
            JSONObject examConfig = schemeJson.getJSONObject("examConfig");
            JSONArray scoreList = examConfig.getJSONArray("scoreList");
            
            if (scoreList == null || scoreList.isEmpty()) {
                return BigDecimal.ZERO;
            }
            
            // 如果用户答案和正确答案都是数组，进行逐项比较
            if (userAnswer instanceof List && correctAnswer instanceof List) {
                List<?> userList = (List<?>) userAnswer;
                List<?> correctList = (List<?>) correctAnswer;
                
                BigDecimal totalScore = BigDecimal.ZERO;
                int minSize = Math.min(userList.size(), Math.min(correctList.size(), scoreList.size()));
                
                for (int i = 0; i < minSize; i++) {
                    if (Objects.equals(userList.get(i), correctList.get(i))) {
                        BigDecimal itemScore = scoreList.getBigDecimal(i);
                        if (itemScore != null) {
                            totalScore = totalScore.add(itemScore);
                        }
                    }
                }
                
                return totalScore;
            }
            
            return BigDecimal.ZERO;
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }
}
