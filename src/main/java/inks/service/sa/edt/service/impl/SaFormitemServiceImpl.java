package inks.service.sa.edt.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaFormitemEntity;
import inks.service.sa.edt.domain.pojo.SaFormitemPojo;
import inks.service.sa.edt.mapper.SaFormitemMapper;
import inks.service.sa.edt.service.SaFormitemService;
import inks.service.sa.edt.utils.SortUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
/**
 * 表单项(SaFormitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-24 17:10:38
 */
@Service("saFormitemService")
public class SaFormitemServiceImpl implements SaFormitemService {
    @Resource
    private SaFormitemMapper saFormitemMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormitemPojo getEntity(String key) {
        return this.saFormitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormitemPojo> lst = saFormitemMapper.getPageList(queryParam);
            PageInfo<SaFormitemPojo> pageInfo = new PageInfo<SaFormitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaFormitemPojo> getList(String Pid) {
        try {
            List<SaFormitemPojo> lst = saFormitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Resource
    private SortUtils sortUtils;
    /**
     * 新增数据
     *
     * @param saFormitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormitemPojo insert(SaFormitemPojo saFormitemPojo) {
        //初始化item的NULL
        SaFormitemPojo itempojo =this.clearNull(saFormitemPojo);
        SaFormitemEntity saFormitemEntity = new SaFormitemEntity();
        BeanUtils.copyProperties(itempojo,saFormitemEntity);
         //生成雪花id
          saFormitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFormitemEntity.setRevision(1);  //乐观锁
        //排序下标计算
        saFormitemEntity.setSort(sortUtils.getInitialSortPosition(saFormitemEntity.getPid()));
          this.saFormitemMapper.insert(saFormitemEntity);
        return this.getEntity(saFormitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saFormitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormitemPojo update(SaFormitemPojo saFormitemPojo) {
        SaFormitemEntity saFormitemEntity = new SaFormitemEntity();
        BeanUtils.copyProperties(saFormitemPojo,saFormitemEntity);
        this.saFormitemMapper.update(saFormitemEntity);
        return this.getEntity(saFormitemEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormitemMapper.delete(key) ;
    }

     /**
     * 修改数据
     *
     * @param saFormitemPojo 实例对象
     * @return 实例对象
     */
     @Override
     public SaFormitemPojo clearNull(SaFormitemPojo saFormitemPojo){
     //初始化NULL字段
     if(saFormitemPojo.getPid()==null) saFormitemPojo.setPid("");
     if(saFormitemPojo.getFormitemid()==null) saFormitemPojo.setFormitemid("");
     if(saFormitemPojo.getType()==null) saFormitemPojo.setType("");
     if(saFormitemPojo.getLabel()==null) saFormitemPojo.setLabel("");
     if(saFormitemPojo.getTextlabel()==null) saFormitemPojo.setTextlabel("");
     if(saFormitemPojo.getIsdisplaytype()==null) saFormitemPojo.setIsdisplaytype(0);
     if(saFormitemPojo.getIshidetype()==null) saFormitemPojo.setIshidetype(0);
     if(saFormitemPojo.getIsspecialtype()==null) saFormitemPojo.setIsspecialtype(0);
     if(saFormitemPojo.getShowlabel()==null) saFormitemPojo.setShowlabel(0);
     if(saFormitemPojo.getDefaultvalue()==null) saFormitemPojo.setDefaultvalue("");
     if(saFormitemPojo.getRequired()==null) saFormitemPojo.setRequired(0);
     if(saFormitemPojo.getPlaceholder()==null) saFormitemPojo.setPlaceholder("");
     if(saFormitemPojo.getSort()==null) saFormitemPojo.setSort(0);
     if(saFormitemPojo.getSpan()==null) saFormitemPojo.setSpan(0);
     if(saFormitemPojo.getScheme()==null) saFormitemPojo.setScheme("{}");
     if(saFormitemPojo.getReglist()==null) saFormitemPojo.setReglist("[]");
     if(saFormitemPojo.getShowcolumn()==null) saFormitemPojo.setShowcolumn(0);
     if(saFormitemPojo.getCustom1()==null) saFormitemPojo.setCustom1("");
     if(saFormitemPojo.getCustom2()==null) saFormitemPojo.setCustom2("");
     if(saFormitemPojo.getCustom3()==null) saFormitemPojo.setCustom3("");
     if(saFormitemPojo.getCustom4()==null) saFormitemPojo.setCustom4("");
     if(saFormitemPojo.getCustom5()==null) saFormitemPojo.setCustom5("");
     if(saFormitemPojo.getTenantid()==null) saFormitemPojo.setTenantid("");
     if(saFormitemPojo.getRevision()==null) saFormitemPojo.setRevision(0);
     return saFormitemPojo;
     }


    @Override
    public Long getLastItemSort(String formKey) {
        return saFormitemMapper.getLastItemSort(formKey);
    }

    @Override
    public void updateBatchById(List<SaFormitemPojo> itemEntityList) {
        List<SaFormitemEntity> lst = new ArrayList<SaFormitemEntity>();
        for (SaFormitemPojo itemEntity : itemEntityList) {
            SaFormitemEntity saFormitemEntity = new SaFormitemEntity();
            BeanUtils.copyProperties(itemEntity,saFormitemEntity);
//            lst.add(saFormitemEntity);
            saFormitemMapper.update(saFormitemEntity);
        }

    }
}
