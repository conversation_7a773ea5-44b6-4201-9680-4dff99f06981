package inks.service.sa.edt.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.edt.domain.pojo.SaQuestionbankPojo;
import inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.edt.domain.pojo.SaQuestionbankitemdetailPojo;
import inks.service.sa.edt.domain.SaQuestionbankEntity;
import inks.service.sa.edt.domain.SaQuestionbankitemEntity;
import inks.service.sa.edt.domain.vo.UserQuestionbankDetailVO;
import inks.service.sa.edt.mapper.SaQuestionbankMapper;
import inks.service.sa.edt.service.SaQuestionbankService;
import inks.service.sa.edt.service.SaQuestionbankitemService;
import inks.service.sa.edt.mapper.SaQuestionbankitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 用户表单(SaQuestionbank)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31 08:53:52
 */
@Service("saQuestionbankService")
public class SaQuestionbankServiceImpl implements SaQuestionbankService {
    @Resource
    private SaQuestionbankMapper saQuestionbankMapper;
    
    @Resource
    private SaQuestionbankitemMapper saQuestionbankitemMapper;
    

    @Resource
    private SaQuestionbankitemService saQuestionbankitemService;
    

    @Override
    public SaQuestionbankPojo getEntity(String key) {
        return this.saQuestionbankMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaQuestionbankitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuestionbankitemdetailPojo> lst = saQuestionbankMapper.getPageList(queryParam);
            PageInfo<SaQuestionbankitemdetailPojo> pageInfo = new PageInfo<SaQuestionbankitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaQuestionbankPojo getBillEntity(String key) {
       try {
        //读取主表
        SaQuestionbankPojo saQuestionbankPojo = this.saQuestionbankMapper.getEntity(key);
        //读取子表
        saQuestionbankPojo.setItem(saQuestionbankitemMapper.getList(saQuestionbankPojo.getId()));
        return saQuestionbankPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaQuestionbankPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuestionbankPojo> lst = saQuestionbankMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saQuestionbankitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaQuestionbankPojo> pageInfo = new PageInfo<SaQuestionbankPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaQuestionbankPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaQuestionbankPojo> lst = saQuestionbankMapper.getPageTh(queryParam);
            PageInfo<SaQuestionbankPojo> pageInfo = new PageInfo<SaQuestionbankPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaQuestionbankPojo insert(SaQuestionbankPojo saQuestionbankPojo) {
        //初始化NULL字段
        cleanNull(saQuestionbankPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaQuestionbankEntity saQuestionbankEntity = new SaQuestionbankEntity(); 
        BeanUtils.copyProperties(saQuestionbankPojo,saQuestionbankEntity);
        //设置id和新建日期
        saQuestionbankEntity.setId(id);
        saQuestionbankEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saQuestionbankMapper.insert(saQuestionbankEntity);
        //Item子表处理
        List<SaQuestionbankitemPojo> lst = saQuestionbankPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for (SaQuestionbankitemPojo saQuestionbankitemPojo : lst) {
                //初始化item的NULL
                SaQuestionbankitemPojo itemPojo = this.saQuestionbankitemService.clearNull(saQuestionbankitemPojo);
                SaQuestionbankitemEntity saQuestionbankitemEntity = new SaQuestionbankitemEntity();
                BeanUtils.copyProperties(itemPojo, saQuestionbankitemEntity);
                //设置id和Pid
                saQuestionbankitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saQuestionbankitemEntity.setPid(id);
                saQuestionbankitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saQuestionbankitemMapper.insert(saQuestionbankitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saQuestionbankEntity.getId());
    }


    @Override
    @Transactional
    public SaQuestionbankPojo update(SaQuestionbankPojo saQuestionbankPojo) {
        //主表更改
        SaQuestionbankEntity saQuestionbankEntity = new SaQuestionbankEntity(); 
        BeanUtils.copyProperties(saQuestionbankPojo,saQuestionbankEntity);
        this.saQuestionbankMapper.update(saQuestionbankEntity);
        if (saQuestionbankPojo.getItem() != null) {
        //Item子表处理
        List<SaQuestionbankitemPojo> lst = saQuestionbankPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saQuestionbankMapper.getDelItemIds(saQuestionbankPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saQuestionbankitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaQuestionbankitemEntity saQuestionbankitemEntity = new SaQuestionbankitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaQuestionbankitemPojo itemPojo =this.saQuestionbankitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saQuestionbankitemEntity);
               //设置id和Pid
               saQuestionbankitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saQuestionbankitemEntity.setPid(saQuestionbankEntity.getId());  // 主表 id
               saQuestionbankitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saQuestionbankitemMapper.insert(saQuestionbankitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saQuestionbankitemEntity);             
               this.saQuestionbankitemMapper.update(saQuestionbankitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saQuestionbankEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaQuestionbankPojo saQuestionbankPojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaQuestionbankitemPojo> lst = saQuestionbankPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saQuestionbankitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.saQuestionbankMapper.delete(key) ;
    }
    

    

    private static void cleanNull(SaQuestionbankPojo saQuestionbankPojo) {
        if(saQuestionbankPojo.getSourceid()==null) saQuestionbankPojo.setSourceid("");
        if(saQuestionbankPojo.getSourcetype()==null) saQuestionbankPojo.setSourcetype(0);
        if(saQuestionbankPojo.getName()==null) saQuestionbankPojo.setName("");
        if(saQuestionbankPojo.getDescription()==null) saQuestionbankPojo.setDescription("");
        if(saQuestionbankPojo.getUserid()==null) saQuestionbankPojo.setUserid("");
        if(saQuestionbankPojo.getFormtype()==null) saQuestionbankPojo.setFormtype("");
        if(saQuestionbankPojo.getStatus()==null) saQuestionbankPojo.setStatus(0);
        if(saQuestionbankPojo.getIsdeleted()==null) saQuestionbankPojo.setIsdeleted(0);
        if(saQuestionbankPojo.getIsfolder()==null) saQuestionbankPojo.setIsfolder(0);
        if(saQuestionbankPojo.getFolderid()==null) saQuestionbankPojo.setFolderid(0);
        if(saQuestionbankPojo.getRemark()==null) saQuestionbankPojo.setRemark("");
        if(saQuestionbankPojo.getCreatebyid()==null) saQuestionbankPojo.setCreatebyid("");
        if(saQuestionbankPojo.getCreateby()==null) saQuestionbankPojo.setCreateby("");
        if(saQuestionbankPojo.getCreatedate()==null) saQuestionbankPojo.setCreatedate(new Date());
        if(saQuestionbankPojo.getListerid()==null) saQuestionbankPojo.setListerid("");
        if(saQuestionbankPojo.getLister()==null) saQuestionbankPojo.setLister("");
        if(saQuestionbankPojo.getModifydate()==null) saQuestionbankPojo.setModifydate(new Date());
        if(saQuestionbankPojo.getCustom1()==null) saQuestionbankPojo.setCustom1("");
        if(saQuestionbankPojo.getCustom2()==null) saQuestionbankPojo.setCustom2("");
        if(saQuestionbankPojo.getCustom3()==null) saQuestionbankPojo.setCustom3("");
        if(saQuestionbankPojo.getCustom4()==null) saQuestionbankPojo.setCustom4("");
        if(saQuestionbankPojo.getCustom5()==null) saQuestionbankPojo.setCustom5("");
        if(saQuestionbankPojo.getDeptid()==null) saQuestionbankPojo.setDeptid("");
        if(saQuestionbankPojo.getTenantid()==null) saQuestionbankPojo.setTenantid("");
        if(saQuestionbankPojo.getRevision()==null) saQuestionbankPojo.setRevision(0);
   }

    @Override
    public UserQuestionbankDetailVO details(String key) {
        SaQuestionbankPojo formPojo = saQuestionbankMapper.getEntity(key);
        List<SaQuestionbankitemPojo> formitemList = saQuestionbankitemMapper.getList(key);

        return new UserQuestionbankDetailVO(formPojo, formitemList,null,null);
    }

}
