package inks.service.sa.edt.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaFormthemegroupEntity;
import inks.service.sa.edt.domain.pojo.SaFormthemegroupPojo;
import inks.service.sa.edt.mapper.SaFormthemegroupMapper;
import inks.service.sa.edt.service.SaFormthemegroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 表单主题分类(SaFormthemegroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
@Service("saFormthemegroupService")
public class SaFormthemegroupServiceImpl implements SaFormthemegroupService {
    @Resource
    private SaFormthemegroupMapper saFormthemegroupMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormthemegroupPojo getEntity(String key) {
        return this.saFormthemegroupMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormthemegroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormthemegroupPojo> lst = saFormthemegroupMapper.getPageList(queryParam);
            PageInfo<SaFormthemegroupPojo> pageInfo = new PageInfo<SaFormthemegroupPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saFormthemegroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormthemegroupPojo insert(SaFormthemegroupPojo saFormthemegroupPojo) {
    //初始化NULL字段
     if(saFormthemegroupPojo.getName()==null) saFormthemegroupPojo.setName("");
     if(saFormthemegroupPojo.getRownum()==null) saFormthemegroupPojo.setRownum(0);
     if(saFormthemegroupPojo.getRemark()==null) saFormthemegroupPojo.setRemark("");
     if(saFormthemegroupPojo.getCreatebyid()==null) saFormthemegroupPojo.setCreatebyid("");
     if(saFormthemegroupPojo.getCreateby()==null) saFormthemegroupPojo.setCreateby("");
     if(saFormthemegroupPojo.getCreatedate()==null) saFormthemegroupPojo.setCreatedate(new Date());
     if(saFormthemegroupPojo.getListerid()==null) saFormthemegroupPojo.setListerid("");
     if(saFormthemegroupPojo.getLister()==null) saFormthemegroupPojo.setLister("");
     if(saFormthemegroupPojo.getModifydate()==null) saFormthemegroupPojo.setModifydate(new Date());
     if(saFormthemegroupPojo.getCustom1()==null) saFormthemegroupPojo.setCustom1("");
     if(saFormthemegroupPojo.getCustom2()==null) saFormthemegroupPojo.setCustom2("");
     if(saFormthemegroupPojo.getCustom3()==null) saFormthemegroupPojo.setCustom3("");
     if(saFormthemegroupPojo.getCustom4()==null) saFormthemegroupPojo.setCustom4("");
     if(saFormthemegroupPojo.getCustom5()==null) saFormthemegroupPojo.setCustom5("");
     if(saFormthemegroupPojo.getTenantid()==null) saFormthemegroupPojo.setTenantid("");
     if(saFormthemegroupPojo.getRevision()==null) saFormthemegroupPojo.setRevision(0);
        SaFormthemegroupEntity saFormthemegroupEntity = new SaFormthemegroupEntity(); 
        BeanUtils.copyProperties(saFormthemegroupPojo,saFormthemegroupEntity);
        //生成雪花id
          saFormthemegroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFormthemegroupEntity.setRevision(1);  //乐观锁
          this.saFormthemegroupMapper.insert(saFormthemegroupEntity);
        return this.getEntity(saFormthemegroupEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFormthemegroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormthemegroupPojo update(SaFormthemegroupPojo saFormthemegroupPojo) {
        SaFormthemegroupEntity saFormthemegroupEntity = new SaFormthemegroupEntity(); 
        BeanUtils.copyProperties(saFormthemegroupPojo,saFormthemegroupEntity);
        this.saFormthemegroupMapper.update(saFormthemegroupEntity);
        return this.getEntity(saFormthemegroupEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormthemegroupMapper.delete(key) ;
    }
    
                                                                                         
}
