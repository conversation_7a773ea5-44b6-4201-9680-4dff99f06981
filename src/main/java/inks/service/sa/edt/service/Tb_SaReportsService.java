package inks.service.sa.edt.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaReportsPojo;

import java.util.List;

/**
 * 报表中心(含Formid)(SaReports)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:49
 */
public interface Tb_SaReportsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReportsPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaReportsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saReportsPojo 实例对象
     * @return 实例对象
     *      * boolean isConversion 是否进行转换(正常情况下需要进行转换(即true) 将前端传来的Rptdata BASE64转换成XML)
     *      * 但是当复制表单的打印模板时,不需要再进行转换了!因为读取的就是转换保存进DB的Rptdata
     */
    SaReportsPojo insert(SaReportsPojo saReportsPojo,boolean isConversion);

    /**
     * 修改数据
     *
     * @param saReportspojo 实例对象
     * @return 实例对象
     */
    SaReportsPojo update(SaReportsPojo saReportspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<SaReportsPojo> pullDefault(String code, LoginUser loginUser);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaReportsPojo> getPageListAll(QueryParam queryParam);

    List<SaReportsPojo> getListByModuleCode(String moduleCode);

    List<SaReportsPojo> getListByFormId(String formid,String code,QueryParam queryParam);
}
