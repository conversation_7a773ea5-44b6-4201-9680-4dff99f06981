package inks.service.sa.edt.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaFormsharePojo;

/**
 * 表单分享表(SaFormshare)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-20 14:18:23
 */
public interface SaFormshareService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormsharePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFormsharePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFormsharePojo 实例对象
     * @return 实例对象
     */
    SaFormsharePojo insert(SaFormsharePojo saFormsharePojo);

    /**
     * 修改数据
     *
     * @param saFormsharepojo 实例对象
     * @return 实例对象
     */
    SaFormsharePojo update(SaFormsharePojo saFormsharepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
                                                                                                         }
