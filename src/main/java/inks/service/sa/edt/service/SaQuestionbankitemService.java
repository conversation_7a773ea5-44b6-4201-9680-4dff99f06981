package inks.service.sa.edt.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo;
import com.github.pagehelper.PageInfo;

import java.util.List;
/**
 * 表单项(SaQuestionbankitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-31 08:54:06
 */
public interface SaQuestionbankitemService {


    SaQuestionbankitemPojo getEntity(String key);

    PageInfo<SaQuestionbankitemPojo> getPageList(QueryParam queryParam);

    List<SaQuestionbankitemPojo> getList(String Pid);  

    SaQuestionbankitemPojo insert(SaQuestionbankitemPojo saQuestionbankitemPojo);

    SaQuestionbankitemPojo update(SaQuestionbankitemPojo saQuestionbankitempojo);

    int delete(String key);

    SaQuestionbankitemPojo clearNull(SaQuestionbankitemPojo saQuestionbankitempojo);

    SaQuestionbankitemPojo getByFormItemId(String formItemId, String formKey);
}
