package inks.service.sa.edt.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaFormlogicEntity;
import inks.service.sa.edt.domain.pojo.SaFormlogicPojo;
import inks.service.sa.edt.mapper.SaFormlogicMapper;
import inks.service.sa.edt.service.SaFormlogicService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 项目逻辑(SaFormlogic)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 15:02:54
 */
@Service("saFormlogicService")
public class SaFormlogicServiceImpl implements SaFormlogicService {
    @Resource
    private SaFormlogicMapper saFormlogicMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFormlogicPojo getEntity(String key) {
        return this.saFormlogicMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFormlogicPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFormlogicPojo> lst = saFormlogicMapper.getPageList(queryParam);
            PageInfo<SaFormlogicPojo> pageInfo = new PageInfo<SaFormlogicPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saFormlogicPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormlogicPojo insert(SaFormlogicPojo saFormlogicPojo) {
    //初始化NULL字段
     if(saFormlogicPojo.getFormid()==null) saFormlogicPojo.setFormid("");
     if(saFormlogicPojo.getScheme()==null) saFormlogicPojo.setScheme("");
     if(saFormlogicPojo.getRemark()==null) saFormlogicPojo.setRemark("");
     if(saFormlogicPojo.getCreatebyid()==null) saFormlogicPojo.setCreatebyid("");
     if(saFormlogicPojo.getCreateby()==null) saFormlogicPojo.setCreateby("");
     if(saFormlogicPojo.getCreatedate()==null) saFormlogicPojo.setCreatedate(new Date());
     if(saFormlogicPojo.getListerid()==null) saFormlogicPojo.setListerid("");
     if(saFormlogicPojo.getLister()==null) saFormlogicPojo.setLister("");
     if(saFormlogicPojo.getModifydate()==null) saFormlogicPojo.setModifydate(new Date());
     if(saFormlogicPojo.getCustom1()==null) saFormlogicPojo.setCustom1("");
     if(saFormlogicPojo.getCustom2()==null) saFormlogicPojo.setCustom2("");
     if(saFormlogicPojo.getCustom3()==null) saFormlogicPojo.setCustom3("");
     if(saFormlogicPojo.getCustom4()==null) saFormlogicPojo.setCustom4("");
     if(saFormlogicPojo.getCustom5()==null) saFormlogicPojo.setCustom5("");
     if(saFormlogicPojo.getTenantid()==null) saFormlogicPojo.setTenantid("");
     if(saFormlogicPojo.getRevision()==null) saFormlogicPojo.setRevision(0);
        SaFormlogicEntity saFormlogicEntity = new SaFormlogicEntity(); 
        BeanUtils.copyProperties(saFormlogicPojo,saFormlogicEntity);
        //生成雪花id
          saFormlogicEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFormlogicEntity.setRevision(1);  //乐观锁
          this.saFormlogicMapper.insert(saFormlogicEntity);
        return this.getEntity(saFormlogicEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFormlogicPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFormlogicPojo update(SaFormlogicPojo saFormlogicPojo) {
        SaFormlogicEntity saFormlogicEntity = new SaFormlogicEntity(); 
        BeanUtils.copyProperties(saFormlogicPojo,saFormlogicEntity);
        this.saFormlogicMapper.update(saFormlogicEntity);
        return this.getEntity(saFormlogicEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFormlogicMapper.delete(key) ;
    }
    
                                                                                         
}
