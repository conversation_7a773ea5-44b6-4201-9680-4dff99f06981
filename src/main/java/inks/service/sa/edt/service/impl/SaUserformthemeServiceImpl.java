package inks.service.sa.edt.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.edt.domain.SaUserformthemeEntity;
import inks.service.sa.edt.domain.pojo.SaUserformthemePojo;
import inks.service.sa.edt.mapper.SaUserformthemeMapper;
import inks.service.sa.edt.service.SaUserformthemeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 项目表单项(SaUserformtheme)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
@Service("saUserformthemeService")
public class SaUserformthemeServiceImpl implements SaUserformthemeService {
    @Resource
    private SaUserformthemeMapper saUserformthemeMapper;
    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaUserformthemePojo getEntity(String key) {
        return this.saUserformthemeMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaUserformthemePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaUserformthemePojo> lst = saUserformthemeMapper.getPageList(queryParam);
            PageInfo<SaUserformthemePojo> pageInfo = new PageInfo<SaUserformthemePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    /**
     * 新增数据
     *
     * @param saUserformthemePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserformthemePojo insert(SaUserformthemePojo saUserformthemePojo) {
    //初始化NULL字段
     if(saUserformthemePojo.getFormid()==null) saUserformthemePojo.setFormid("");
     if(saUserformthemePojo.getSubmitbtntext()==null) saUserformthemePojo.setSubmitbtntext("");
     if(saUserformthemePojo.getLogoimg()==null) saUserformthemePojo.setLogoimg("");
     if(saUserformthemePojo.getLogoposition()==null) saUserformthemePojo.setLogoposition("");
     if(saUserformthemePojo.getBackcolor()==null) saUserformthemePojo.setBackcolor("");
     if(saUserformthemePojo.getBackimg()==null) saUserformthemePojo.setBackimg("");
     if(saUserformthemePojo.getShowtitle()==null) saUserformthemePojo.setShowtitle(0);
     if(saUserformthemePojo.getShowdesc()==null) saUserformthemePojo.setShowdesc(0);
     if(saUserformthemePojo.getThemecolor()==null) saUserformthemePojo.setThemecolor("");
     if(saUserformthemePojo.getShownumber()==null) saUserformthemePojo.setShownumber(0);
     if(saUserformthemePojo.getShowsubmitbtn()==null) saUserformthemePojo.setShowsubmitbtn(0);
     if(saUserformthemePojo.getHeadimgurl()==null) saUserformthemePojo.setHeadimgurl("");
     if(saUserformthemePojo.getRemark()==null) saUserformthemePojo.setRemark("");
     if(saUserformthemePojo.getCreatebyid()==null) saUserformthemePojo.setCreatebyid("");
     if(saUserformthemePojo.getCreateby()==null) saUserformthemePojo.setCreateby("");
     if(saUserformthemePojo.getCreatedate()==null) saUserformthemePojo.setCreatedate(new Date());
     if(saUserformthemePojo.getListerid()==null) saUserformthemePojo.setListerid("");
     if(saUserformthemePojo.getLister()==null) saUserformthemePojo.setLister("");
     if(saUserformthemePojo.getModifydate()==null) saUserformthemePojo.setModifydate(new Date());
     if(saUserformthemePojo.getCustom1()==null) saUserformthemePojo.setCustom1("");
     if(saUserformthemePojo.getCustom2()==null) saUserformthemePojo.setCustom2("");
     if(saUserformthemePojo.getCustom3()==null) saUserformthemePojo.setCustom3("");
     if(saUserformthemePojo.getCustom4()==null) saUserformthemePojo.setCustom4("");
     if(saUserformthemePojo.getCustom5()==null) saUserformthemePojo.setCustom5("");
     if(saUserformthemePojo.getTenantid()==null) saUserformthemePojo.setTenantid("");
     if(saUserformthemePojo.getRevision()==null) saUserformthemePojo.setRevision(0);
        SaUserformthemeEntity saUserformthemeEntity = new SaUserformthemeEntity(); 
        BeanUtils.copyProperties(saUserformthemePojo,saUserformthemeEntity);
        //生成雪花id
          saUserformthemeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saUserformthemeEntity.setRevision(1);  //乐观锁
          this.saUserformthemeMapper.insert(saUserformthemeEntity);
        return this.getEntity(saUserformthemeEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saUserformthemePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserformthemePojo update(SaUserformthemePojo saUserformthemePojo) {
        SaUserformthemeEntity saUserformthemeEntity = new SaUserformthemeEntity(); 
        BeanUtils.copyProperties(saUserformthemePojo,saUserformthemeEntity);
        this.saUserformthemeMapper.update(saUserformthemeEntity);
        return this.getEntity(saUserformthemeEntity.getId());
    }
    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saUserformthemeMapper.delete(key) ;
    }
    
                                                                                                                                           
}
