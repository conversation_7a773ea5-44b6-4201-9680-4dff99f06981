package inks.service.sa.edt.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 题库题目操作返回数据
 * 
 * <AUTHOR>
 * @create 2025-07-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperateQuestionItemVO {

    /**
     * 排序号
     */
    private Long sort;

    /**
     * 题目数据Id
     */
    private String itemDataId;

    /**
     * 操作是否成功
     */
    private Boolean operateSuccess;

    /**
     * 刷新全部
     */
    private Boolean refreshAll;
}
