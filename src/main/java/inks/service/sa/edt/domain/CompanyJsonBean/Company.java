/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.edt.domain.CompanyJsonBean;

import java.util.List;

/**
 * Auto-generated: 2022-09-06 9:4:55
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class Company {

    private String historyNames;
    private String regStatus;
    private String estiblishTimeTitleName;
    private int websiteRiskType;
    private List<String> emailList;
    private List<String> listedPlateList;
    private List<String> phoneList;
    private String baiduAuthURLWWW;
    private int type;
    private String inverstStatus;
    private String equityUrl;
    private int legalPersonType;
    private int sensitiveEntityType;
    private String property3;
    private String companyShowBizTypeName;
    private String regCapitalLabel;
    private String companyProfilePlainText;
    private long approvedTime;
    private String industry2017;
    private String logo;
    private long id;
    private int originalPercentileScore;
    private String orgNumber;
    private int isClaimed;
    private int listedStatusTypeForSenior;
//    private CompanyBaseInfoSubtitleList companyBaseInfoSubtitleList;
    private String taxPhone;
    private String taxBankName;
    private int entityType;
    private String companyBizOrgType;
    private String businessScope;
    private String taxNumber;
    private String portray;
    private String regCapitalCurrency;
    private String regCapitalAmount;
    private String phoneNumber;
    private String taxQualification;
    private String name;
    private int percentileScore;
    private String baseInfo;
    private String regCapital;
    private String regLocationTitle;
    private String staffNumRange;
    private int link;
    private String industry;
    private String legalTitleName;
    private String regTitleName;
    private long updateTimes;
    private String legalPersonName;
    private String regNumber;
    private String creditCode;
    private long fromTime;
    private int socialStaffNum;
    private String companyOrgType;
    private String alias;
    private String baiduAuthURLWAP;
    private String taxAddress;
    private long toTime;
    private String email;
    private String actualCapital;
    private List<PhoneSourceList> phoneSourceList;
    private long estiblishTime;
    private String taxBankAccount;
    private String regInstitute;
    private int listedStatusType;
    private int companyBizType;
    private String regLocation;
    private String regCapitalAmountUnit;
    private String websiteList;
    private String safetype;
    private List<TagList> tagList;
    private long legalPersonId;
    private String complexName;
    private List<CompanyProfileRichText> companyProfileRichText;
    private LegalInfo legalInfo;
    private long updatetime;
    private String base;

    public String getHistoryNames() {
        return historyNames;
    }

    public void setHistoryNames(String historyNames) {
        this.historyNames = historyNames;
    }

    public String getRegStatus() {
        return regStatus;
    }

    public void setRegStatus(String regStatus) {
        this.regStatus = regStatus;
    }

    public String getEstiblishTimeTitleName() {
        return estiblishTimeTitleName;
    }

    public void setEstiblishTimeTitleName(String estiblishTimeTitleName) {
        this.estiblishTimeTitleName = estiblishTimeTitleName;
    }

    public int getWebsiteRiskType() {
        return websiteRiskType;
    }

    public void setWebsiteRiskType(int websiteRiskType) {
        this.websiteRiskType = websiteRiskType;
    }

    public List<String> getEmailList() {
        return emailList;
    }

    public void setEmailList(List<String> emailList) {
        this.emailList = emailList;
    }

    public List<String> getListedPlateList() {
        return listedPlateList;
    }

    public void setListedPlateList(List<String> listedPlateList) {
        this.listedPlateList = listedPlateList;
    }

    public List<String> getPhoneList() {
        return phoneList;
    }

    public void setPhoneList(List<String> phoneList) {
        this.phoneList = phoneList;
    }

    public String getBaiduAuthURLWWW() {
        return baiduAuthURLWWW;
    }

    public void setBaiduAuthURLWWW(String baiduAuthURLWWW) {
        this.baiduAuthURLWWW = baiduAuthURLWWW;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getInverstStatus() {
        return inverstStatus;
    }

    public void setInverstStatus(String inverstStatus) {
        this.inverstStatus = inverstStatus;
    }

    public String getEquityUrl() {
        return equityUrl;
    }

    public void setEquityUrl(String equityUrl) {
        this.equityUrl = equityUrl;
    }

    public int getLegalPersonType() {
        return legalPersonType;
    }

    public void setLegalPersonType(int legalPersonType) {
        this.legalPersonType = legalPersonType;
    }

    public int getSensitiveEntityType() {
        return sensitiveEntityType;
    }

    public void setSensitiveEntityType(int sensitiveEntityType) {
        this.sensitiveEntityType = sensitiveEntityType;
    }

    public String getProperty3() {
        return property3;
    }

    public void setProperty3(String property3) {
        this.property3 = property3;
    }

    public String getCompanyShowBizTypeName() {
        return companyShowBizTypeName;
    }

    public void setCompanyShowBizTypeName(String companyShowBizTypeName) {
        this.companyShowBizTypeName = companyShowBizTypeName;
    }

    public String getRegCapitalLabel() {
        return regCapitalLabel;
    }

    public void setRegCapitalLabel(String regCapitalLabel) {
        this.regCapitalLabel = regCapitalLabel;
    }

    public String getCompanyProfilePlainText() {
        return companyProfilePlainText;
    }

    public void setCompanyProfilePlainText(String companyProfilePlainText) {
        this.companyProfilePlainText = companyProfilePlainText;
    }

    public long getApprovedTime() {
        return approvedTime;
    }

    public void setApprovedTime(long approvedTime) {
        this.approvedTime = approvedTime;
    }

    public String getIndustry2017() {
        return industry2017;
    }

    public void setIndustry2017(String industry2017) {
        this.industry2017 = industry2017;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getOriginalPercentileScore() {
        return originalPercentileScore;
    }

    public void setOriginalPercentileScore(int originalPercentileScore) {
        this.originalPercentileScore = originalPercentileScore;
    }

    public String getOrgNumber() {
        return orgNumber;
    }

    public void setOrgNumber(String orgNumber) {
        this.orgNumber = orgNumber;
    }

    public int getIsClaimed() {
        return isClaimed;
    }

    public void setIsClaimed(int isClaimed) {
        this.isClaimed = isClaimed;
    }

    public int getListedStatusTypeForSenior() {
        return listedStatusTypeForSenior;
    }

    public void setListedStatusTypeForSenior(int listedStatusTypeForSenior) {
        this.listedStatusTypeForSenior = listedStatusTypeForSenior;
    }

    public String getTaxPhone() {
        return taxPhone;
    }

    public void setTaxPhone(String taxPhone) {
        this.taxPhone = taxPhone;
    }

    public String getTaxBankName() {
        return taxBankName;
    }

    public void setTaxBankName(String taxBankName) {
        this.taxBankName = taxBankName;
    }

    public int getEntityType() {
        return entityType;
    }

    public void setEntityType(int entityType) {
        this.entityType = entityType;
    }

    public String getCompanyBizOrgType() {
        return companyBizOrgType;
    }

    public void setCompanyBizOrgType(String companyBizOrgType) {
        this.companyBizOrgType = companyBizOrgType;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getPortray() {
        return portray;
    }

    public void setPortray(String portray) {
        this.portray = portray;
    }

    public String getRegCapitalCurrency() {
        return regCapitalCurrency;
    }

    public void setRegCapitalCurrency(String regCapitalCurrency) {
        this.regCapitalCurrency = regCapitalCurrency;
    }

    public String getRegCapitalAmount() {
        return regCapitalAmount;
    }

    public void setRegCapitalAmount(String regCapitalAmount) {
        this.regCapitalAmount = regCapitalAmount;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getTaxQualification() {
        return taxQualification;
    }

    public void setTaxQualification(String taxQualification) {
        this.taxQualification = taxQualification;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPercentileScore() {
        return percentileScore;
    }

    public void setPercentileScore(int percentileScore) {
        this.percentileScore = percentileScore;
    }

    public String getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(String baseInfo) {
        this.baseInfo = baseInfo;
    }

    public String getRegCapital() {
        return regCapital;
    }

    public void setRegCapital(String regCapital) {
        this.regCapital = regCapital;
    }

    public String getRegLocationTitle() {
        return regLocationTitle;
    }

    public void setRegLocationTitle(String regLocationTitle) {
        this.regLocationTitle = regLocationTitle;
    }

    public String getStaffNumRange() {
        return staffNumRange;
    }

    public void setStaffNumRange(String staffNumRange) {
        this.staffNumRange = staffNumRange;
    }

    public int getLink() {
        return link;
    }

    public void setLink(int link) {
        this.link = link;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getLegalTitleName() {
        return legalTitleName;
    }

    public void setLegalTitleName(String legalTitleName) {
        this.legalTitleName = legalTitleName;
    }

    public String getRegTitleName() {
        return regTitleName;
    }

    public void setRegTitleName(String regTitleName) {
        this.regTitleName = regTitleName;
    }

    public long getUpdateTimes() {
        return updateTimes;
    }

    public void setUpdateTimes(long updateTimes) {
        this.updateTimes = updateTimes;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getRegNumber() {
        return regNumber;
    }

    public void setRegNumber(String regNumber) {
        this.regNumber = regNumber;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public long getFromTime() {
        return fromTime;
    }

    public void setFromTime(long fromTime) {
        this.fromTime = fromTime;
    }

    public int getSocialStaffNum() {
        return socialStaffNum;
    }

    public void setSocialStaffNum(int socialStaffNum) {
        this.socialStaffNum = socialStaffNum;
    }

    public String getCompanyOrgType() {
        return companyOrgType;
    }

    public void setCompanyOrgType(String companyOrgType) {
        this.companyOrgType = companyOrgType;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getBaiduAuthURLWAP() {
        return baiduAuthURLWAP;
    }

    public void setBaiduAuthURLWAP(String baiduAuthURLWAP) {
        this.baiduAuthURLWAP = baiduAuthURLWAP;
    }

    public String getTaxAddress() {
        return taxAddress;
    }

    public void setTaxAddress(String taxAddress) {
        this.taxAddress = taxAddress;
    }

    public long getToTime() {
        return toTime;
    }

    public void setToTime(long toTime) {
        this.toTime = toTime;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getActualCapital() {
        return actualCapital;
    }

    public void setActualCapital(String actualCapital) {
        this.actualCapital = actualCapital;
    }

    public List<PhoneSourceList> getPhoneSourceList() {
        return phoneSourceList;
    }

    public void setPhoneSourceList(List<PhoneSourceList> phoneSourceList) {
        this.phoneSourceList = phoneSourceList;
    }

    public long getEstiblishTime() {
        return estiblishTime;
    }

    public void setEstiblishTime(long estiblishTime) {
        this.estiblishTime = estiblishTime;
    }

    public String getTaxBankAccount() {
        return taxBankAccount;
    }

    public void setTaxBankAccount(String taxBankAccount) {
        this.taxBankAccount = taxBankAccount;
    }

    public String getRegInstitute() {
        return regInstitute;
    }

    public void setRegInstitute(String regInstitute) {
        this.regInstitute = regInstitute;
    }

    public int getListedStatusType() {
        return listedStatusType;
    }

    public void setListedStatusType(int listedStatusType) {
        this.listedStatusType = listedStatusType;
    }

    public int getCompanyBizType() {
        return companyBizType;
    }

    public void setCompanyBizType(int companyBizType) {
        this.companyBizType = companyBizType;
    }

    public String getRegLocation() {
        return regLocation;
    }

    public void setRegLocation(String regLocation) {
        this.regLocation = regLocation;
    }

    public String getRegCapitalAmountUnit() {
        return regCapitalAmountUnit;
    }

    public void setRegCapitalAmountUnit(String regCapitalAmountUnit) {
        this.regCapitalAmountUnit = regCapitalAmountUnit;
    }

    public String getWebsiteList() {
        return websiteList;
    }

    public void setWebsiteList(String websiteList) {
        this.websiteList = websiteList;
    }

    public String getSafetype() {
        return safetype;
    }

    public void setSafetype(String safetype) {
        this.safetype = safetype;
    }

    public List<TagList> getTagList() {
        return tagList;
    }

    public void setTagList(List<TagList> tagList) {
        this.tagList = tagList;
    }

    public long getLegalPersonId() {
        return legalPersonId;
    }

    public void setLegalPersonId(long legalPersonId) {
        this.legalPersonId = legalPersonId;
    }

    public String getComplexName() {
        return complexName;
    }

    public void setComplexName(String complexName) {
        this.complexName = complexName;
    }

    public List<CompanyProfileRichText> getCompanyProfileRichText() {
        return companyProfileRichText;
    }

    public void setCompanyProfileRichText(List<CompanyProfileRichText> companyProfileRichText) {
        this.companyProfileRichText = companyProfileRichText;
    }

    public LegalInfo getLegalInfo() {
        return legalInfo;
    }

    public void setLegalInfo(LegalInfo legalInfo) {
        this.legalInfo = legalInfo;
    }

    public long getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(long updatetime) {
        this.updatetime = updatetime;
    }

    public String getBase() {
        return base;
    }

    public void setBase(String base) {
        this.base = base;
    }
}