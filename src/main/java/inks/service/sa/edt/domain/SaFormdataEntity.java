package inks.service.sa.edt.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 表单收集数据结果(SaFormdata)实体类
 *
 * <AUTHOR>
 * @since 2025-07-10 10:45:11
 */
@Data
public class SaFormdataEntity implements Serializable {
    private static final long serialVersionUID = 116189876006557361L;
    private String id;
     // 表单key
    private String formid;
     // 序号
    private Integer seqlnum;
     // 填写结果
    private String originaldata;
     // OriginalData的单选多选数字转换为实际值
    private String realdata;
     // 提交ua
    private String useragent;
     // 提交系统
    private String subos;
     // 提交浏览器
    private String subbrowser;
     // 请求ip
    private String subreqip;
     // 提交地址
    private String subaddr;
     // 完成时间 毫秒
    private Integer completetime;
     // 微信openId
    private String wxopenid;
     // 微信用户信息
    private String wxuserinfo;
     // 扩展字段记录来源等
    private String extvalue;
     // 备注
    private String remark;
     // 是否提交
    private Integer submit;
     // 用户得分
    private Double totalscore;
     // 试卷总分
    private Double sumtotalscore;
     // 得分详情
    private String scoredetail;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 审核状态
    private String assessstatus;
     // 创建者id
    private String createbyid;
     // 创建者
    private String createby;
     // 新建日期
    private Date createdate;
     // 制表id
    private String listerid;
     // 制表
    private String lister;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 组织id
    private String deptid;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;



}

