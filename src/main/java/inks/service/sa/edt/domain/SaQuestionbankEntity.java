package inks.service.sa.edt.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 用户表单(SaQuestionbank)实体类
 *
 * <AUTHOR>
 * @since 2025-07-31 08:53:50
 */
@Data
public class SaQuestionbankEntity implements Serializable {
    private static final long serialVersionUID = -44106181741807517L;
     // 题库ID
    private String id;
     // 来源Id
    private String sourceid;
     // 来源类型1空白创建2模板
    private Integer sourcetype;
     // 表单名称
    private String name;
     // 表单描述
    private String description;
     // 用户ID
    private String userid;
     // 表单类型
    private String formtype;
     // 状态:1未发布2收集中3停止发布
    private Integer status;
     // 是否删除
    private Integer isdeleted;
     // 是文件夹
    private Integer isfolder;
     // 文件夹Id
    private Integer folderid;
     // 备注
    private String remark;
     // 创建者id
    private String createbyid;
     // 创建者
    private String createby;
     // 新建日期
    private Date createdate;
     // 制表id
    private String listerid;
     // 制表
    private String lister;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 组织id
    private String deptid;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

