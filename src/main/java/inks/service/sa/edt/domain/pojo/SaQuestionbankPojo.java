package inks.service.sa.edt.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 用户表单(SaQuestionbank)实体类
 *
 * <AUTHOR>
 * @since 2025-07-31 08:53:51
 */
@Data
public class SaQuestionbankPojo implements Serializable {
    private static final long serialVersionUID = -55550174520951566L;
     // 题库ID
     @Excel(name = "题库ID")
    private String id;
     // 来源Id
     @Excel(name = "来源Id")
    private String sourceid;
     // 来源类型1空白创建2模板
     @Excel(name = "来源类型1空白创建2模板")
    private Integer sourcetype;
     // 表单名称
     @Excel(name = "表单名称")
    private String name;
     // 表单描述
     @Excel(name = "表单描述")
    private String description;
     // 用户ID
     @Excel(name = "用户ID")
    private String userid;
     // 表单类型
     @Excel(name = "表单类型")
    private String formtype;
     // 状态:1未发布2收集中3停止发布
     @Excel(name = "状态:1未发布2收集中3停止发布")
    private Integer status;
     // 是否删除
     @Excel(name = "是否删除")
    private Integer isdeleted;
     // 是文件夹
     @Excel(name = "是文件夹")
    private Integer isfolder;
     // 文件夹Id
     @Excel(name = "文件夹Id")
    private Integer folderid;
     // 备注
     @Excel(name = "备注")
    private String remark;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 组织id
     @Excel(name = "组织id")
    private String deptid;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaQuestionbankitemPojo> item;
    

}

