package inks.service.sa.edt.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 表单模板(SaTemplate)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:28
 */
public class SaTemplateEntity implements Serializable {
    private static final long serialVersionUID = -14959897418562901L;
         // 模板Id
         private String id;
         // 模板唯一标识
         private String formid;
         // 封面图
         private String coverimg;
         // 模板名称
         private String name;
         // 模板I描述
         private String description;
         // 模板类型
         private Integer groupid;
         // 模板定义
         private String scheme;
         // 状态
         private Integer status;
         // 备注
         private String remark;
         // 创建者id
         private String createbyid;
         // 创建者
         private String createby;
         // 新建日期
         private Date createdate;
         // 制表id
         private String listerid;
         // 制表
         private String lister;
         // 修改日期
         private Date modifydate;
         // 自定义1
         private String custom1;
         // 自定义2
         private String custom2;
         // 自定义3
         private String custom3;
         // 自定义4
         private String custom4;
         // 自定义5
         private String custom5;
         // 租户id
         private String tenantid;
         // 乐观锁
         private Integer revision;

// 模板Id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
// 模板唯一标识
    public String getFormid() {
        return formid;
    }
    
    public void setFormid(String formid) {
        this.formid = formid;
    }
        
// 封面图
    public String getCoverimg() {
        return coverimg;
    }
    
    public void setCoverimg(String coverimg) {
        this.coverimg = coverimg;
    }
        
// 模板名称
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
        
// 模板I描述
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
        
// 模板类型
    public Integer getGroupid() {
        return groupid;
    }
    
    public void setGroupid(Integer groupid) {
        this.groupid = groupid;
    }
        
// 模板定义
    public String getScheme() {
        return scheme;
    }
    
    public void setScheme(String scheme) {
        this.scheme = scheme;
    }
        
// 状态
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
        
// 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
// 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
// 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
// 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
// 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
// 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
// 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
// 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
// 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
// 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
// 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
// 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
// 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
// 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

