package inks.service.sa.edt.domain.pojo;

import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 表单项(SaQuestionbankitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-07-31 08:54:04
 */
@Data
public class SaQuestionbankitemPojo implements Serializable {
    private static final long serialVersionUID = -20575778662325612L;
     // 题目ID
  @Excel(name = "题目ID")    
  private String id;
     // 表单key
  @Excel(name = "表单key")    
  private String pid;
     // FormItemId
  @Excel(name = "FormItemId")    
  private String formitemid;
     // 表单项类型 
  @Excel(name = "表单项类型 ")    
  private String type;
     // 表单项标题
  @Excel(name = "表单项标题")    
  private String label;
     // TextLabel
  @Excel(name = "TextLabel")    
  private String textlabel;
     // 展示类型组件
  @Excel(name = "展示类型组件")    
  private Integer isdisplaytype;
     // 隐藏类型组件
  @Excel(name = "隐藏类型组件")    
  private Integer ishidetype;
     // 特殊处理类型
  @Excel(name = "特殊处理类型")    
  private Integer isspecialtype;
     // 是否显示标签
  @Excel(name = "是否显示标签")    
  private Integer showlabel;
     // 表单项默认值
  @Excel(name = "表单项默认值")    
  private String defaultvalue;
     // 是否必填
  @Excel(name = "是否必填")    
  private Integer required;
     // 输入型提示文字
  @Excel(name = "输入型提示文字")    
  private String placeholder;
     // 排序
  @Excel(name = "排序")    
  private Integer sort;
     // 栅格宽度
  @Excel(name = "栅格宽度")    
  private Integer span;
     // 表表单原始JSON
  @Excel(name = "表表单原始JSON")    
  private String scheme;
     // 正则表达式 
  @Excel(name = "正则表达式 ")    
  private String reglist;
     // 1显示列0不显示
  @Excel(name = "1显示列0不显示")    
  private Integer showcolumn;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;


}

