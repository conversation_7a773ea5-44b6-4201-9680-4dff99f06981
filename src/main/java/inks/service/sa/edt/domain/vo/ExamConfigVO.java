package inks.service.sa.edt.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 考试配置VO
 * 用于标准化scheme字段中的examConfig结构
 * 
 * <AUTHOR>
 * @since 2025-07-09
 */
public class ExamConfigVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 题目分值
     */
    private BigDecimal score;

    /**
     * 正确答案
     * - 单选题：[2] 表示选项2是正确答案
     * - 多选题：[1,3,4] 表示选项1,3,4是正确答案
     * - 填空题：["答案文本"] 或 "答案文本"
     * - 横向填空：["答案1","答案2","答案3"]
     */
    private Object answer;

    /**
     * 是否启用评分
     */
    private Boolean enableScore;

    /**
     * 评分类型
     * 0: 不评分
     * 1: 自动评分-单选/多选
     * 2: 手动评分-主观题
     * 5: 自动评分-横向填空
     */
    private Integer scoringType;

    /**
     * 答案解析
     */
    private String answerAnalysis;

    /**
     * 是否显示答案
     */
    private Boolean showAnswer;

    /**
     * 横向填空多点评分
     * 用于HORIZONTAL_INPUT类型，每个填空的分值
     */
    private List<BigDecimal> scoreList;

    /**
     * 构造函数
     */
    public ExamConfigVO() {
        this.enableScore = true;
        this.scoringType = 1;
        this.showAnswer = false;
        this.score = BigDecimal.ZERO;
    }

    /**
     * 创建默认的examConfig
     */
    public static ExamConfigVO createDefault() {
        ExamConfigVO config = new ExamConfigVO();
        config.setScore(new BigDecimal("10"));
        config.setEnableScore(true);
        config.setScoringType(1);
        config.setShowAnswer(false);
        config.setAnswerAnalysis("");
        return config;
    }

    /**
     * 根据题目类型创建默认配置
     */
    public static ExamConfigVO createByQuestionType(String questionType) {
        ExamConfigVO config = createDefault();
        
        switch (questionType.toUpperCase()) {
            case "RADIO":
            case "SELECT":
                config.setScoringType(1); // 自动评分
                break;
            case "CHECKBOX":
                config.setScoringType(1); // 自动评分
                break;
            case "INPUT":
            case "TEXTAREA":
                config.setScoringType(2); // 手动评分
                break;
            case "HORIZONTAL_INPUT":
                config.setScoringType(5); // 横向填空评分
                break;
            case "SIGN_PAD":
                config.setScoringType(0); // 不评分
                break;
            default:
                config.setScoringType(1); // 默认自动评分
        }
        
        return config;
    }

    /**
     * 验证examConfig配置的完整性
     */
    public boolean isValid() {
        if (score == null || score.compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }
        if (enableScore == null) {
            return false;
        }
        if (scoringType == null || scoringType < 0 || scoringType > 5) {
            return false;
        }
        // 如果启用评分且为自动评分，必须有正确答案
        if (enableScore && (scoringType == 1 || scoringType == 5) && answer == null) {
            return false;
        }
        return true;
    }

    // Getter and Setter methods
    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public Object getAnswer() {
        return answer;
    }

    public void setAnswer(Object answer) {
        this.answer = answer;
    }

    public Boolean getEnableScore() {
        return enableScore;
    }

    public void setEnableScore(Boolean enableScore) {
        this.enableScore = enableScore;
    }

    public Integer getScoringType() {
        return scoringType;
    }

    public void setScoringType(Integer scoringType) {
        this.scoringType = scoringType;
    }

    public String getAnswerAnalysis() {
        return answerAnalysis;
    }

    public void setAnswerAnalysis(String answerAnalysis) {
        this.answerAnalysis = answerAnalysis;
    }

    public Boolean getShowAnswer() {
        return showAnswer;
    }

    public void setShowAnswer(Boolean showAnswer) {
        this.showAnswer = showAnswer;
    }

    public List<BigDecimal> getScoreList() {
        return scoreList;
    }

    public void setScoreList(List<BigDecimal> scoreList) {
        this.scoreList = scoreList;
    }

    @Override
    public String toString() {
        return "ExamConfigVO{" +
                "score=" + score +
                ", answer=" + answer +
                ", enableScore=" + enableScore +
                ", scoringType=" + scoringType +
                ", answerAnalysis='" + answerAnalysis + '\'' +
                ", showAnswer=" + showAnswer +
                ", scoreList=" + scoreList +
                '}';
    }
}
