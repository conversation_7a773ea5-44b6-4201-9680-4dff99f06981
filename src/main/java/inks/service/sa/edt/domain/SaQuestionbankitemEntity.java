package inks.service.sa.edt.domain;

import java.io.Serializable;
import lombok.Data;

/**
 * 表单项(SaQuestionbankitem)Entity
 *
 * <AUTHOR>
 * @since 2025-07-31 08:54:03
 */
@Data
public class SaQuestionbankitemEntity implements Serializable {
    private static final long serialVersionUID = -29002032009639357L;
     // 题目ID
    private String id;
     // 表单key
    private String pid;
     // FormItemId
    private String formitemid;
     // 表单项类型 
    private String type;
     // 表单项标题
    private String label;
     // TextLabel
    private String textlabel;
     // 展示类型组件
    private Integer isdisplaytype;
     // 隐藏类型组件
    private Integer ishidetype;
     // 特殊处理类型
    private Integer isspecialtype;
     // 是否显示标签
    private Integer showlabel;
     // 表单项默认值
    private String defaultvalue;
     // 是否必填
    private Integer required;
     // 输入型提示文字
    private String placeholder;
     // 排序
    private Integer sort;
     // 栅格宽度
    private Integer span;
     // 表表单原始JSON
    private String scheme;
     // 正则表达式 
    private String reglist;
     // 1显示列0不显示
    private Integer showcolumn;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

