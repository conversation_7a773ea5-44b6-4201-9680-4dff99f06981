/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.edt.domain.CompanyJsonBean;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */
public class PageProps {

    private DehydratedState dehydratedState;
    public void setDehydratedState(DehydratedState dehydratedState) {
         this.dehydratedState = dehydratedState;
     }
     public DehydratedState getDehydratedState() {
         return dehydratedState;
     }

}