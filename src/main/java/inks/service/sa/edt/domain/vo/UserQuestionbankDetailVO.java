package inks.service.sa.edt.domain.vo;

import inks.service.sa.edt.domain.pojo.*;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;


// 注意！ 【虽然是题库视图】，但是前端为了复用表单控件组件，需要返回和表单一样的数据 所以参数名来自表单叫form、formItems、userFormTheme、formLogic
@Data
@AllArgsConstructor
public class UserQuestionbankDetailVO {
    /**
     * 表单基础信息
     */
    private SaQuestionbankPojo form;

    /**
     * 表单项
     */
    private List<SaQuestionbankitemPojo> formItems;

    /**
     * 主题
     */
    private SaFormthemePojo userFormTheme;

    /**
     * 逻辑
     */
    private SaFormlogicPojo formLogic;


}
