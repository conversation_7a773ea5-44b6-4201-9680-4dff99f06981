package inks.service.sa.edt.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户表单(SaForm)实体类
 *
 * <AUTHOR>
 * @since 2023-08-11 14:12:56
 */
public class SaFormPojo implements Serializable {
    private static final long serialVersionUID = 984099384016330027L;
         @Excel(name = "")
    private String id;
         // 来源Id
          @Excel(name = "来源Id")
    private String sourceid;
         // 来源类型 1空白创建2模板
          @Excel(name = "来源类型")
    private Integer sourcetype;
         // 表单名称
          @Excel(name = "表单名称")
    private String name;
         // 表单描述
          @Excel(name = "表单描述")
    private String description;
         // 用户ID
          @Excel(name = "用户ID")
    private String userid;
         // 表单类型
          @Excel(name = "表单类型")
    private String formtype;
         // 状态 状态:1未发布2收集中3停止发布
          @Excel(name = "状态")
    private Integer status;
         // 是否删除
          @Excel(name = "是否删除")
    private Integer isdeleted;
         // 是文件夹
          @Excel(name = "是文件夹")
    private Integer isfolder;
         // 文件夹Id
          @Excel(name = "文件夹Id")
    private Integer folderid;
         // 备注
          @Excel(name = "备注")
    private String remark;
         // 创建者id
          @Excel(name = "创建者id")
    private String createbyid;
         // 创建者
          @Excel(name = "创建者")
    private String createby;
         // 新建日期
          @Excel(name = "新建日期")
    private Date createdate;
         // 制表id
          @Excel(name = "制表id")
    private String listerid;
         // 制表
          @Excel(name = "制表")
    private String lister;
         // 修改日期
          @Excel(name = "修改日期")
    private Date modifydate;
         // 自定义1
          @Excel(name = "自定义1")
    private String custom1;
         // 自定义2
          @Excel(name = "自定义2")
    private String custom2;
         // 自定义3
          @Excel(name = "自定义3")
    private String custom3;
         // 自定义4
          @Excel(name = "自定义4")
    private String custom4;
         // 自定义5
          @Excel(name = "自定义5")
    private String custom5;
    // 组织id
    @Excel(name = "组织id")
    private String deptid;
         // 租户id
          @Excel(name = "租户id")
    private String tenantid;
         // 乐观锁
          @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaFormitemPojo> item;

    // ======FormDate的5个提交/审核字段:=======
    // 是否提交
    @Excel(name = "是否提交")
    private Integer submit;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
    // 审核状态
    @Excel(name = "审核状态")
    private String assessstatus;
    // formdataid
    private String formdataid;

     public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFormdataid() {
        return formdataid;
    }

    public void setFormdataid(String formdataid) {
        this.formdataid = formdataid;
    }

    // 来源Id
     public String getSourceid() {
        return sourceid;
    }

    public void setSourceid(String sourceid) {
        this.sourceid = sourceid;
    }

   // 来源类型 1空白创建2模板
     public Integer getSourcetype() {
        return sourcetype;
    }

    public void setSourcetype(Integer sourcetype) {
        this.sourcetype = sourcetype;
    }

   // 表单名称
     public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

   // 表单描述
     public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

   // 用户ID
     public String getUserid() {
        return userid;
    }

    public Integer getSubmit() {
        return submit;
    }

    public void setSubmit(Integer submit) {
        this.submit = submit;
    }

    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    public String getAssessorid() {
        return assessorid;
    }

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    public String getAssessstatus() {
        return assessstatus;
    }

    public void setAssessstatus(String assessstatus) {
        this.assessstatus = assessstatus;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

   // 表单类型
     public String getFormtype() {
        return formtype;
    }

    public void setFormtype(String formtype) {
        this.formtype = formtype;
    }

   // 状态 状态:1未发布2收集中3停止发布
     public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

   // 是否删除
     public Integer getIsdeleted() {
        return isdeleted;
    }

    public void setIsdeleted(Integer isdeleted) {
        this.isdeleted = isdeleted;
    }

   // 是文件夹
     public Integer getIsfolder() {
        return isfolder;
    }

    public void setIsfolder(Integer isfolder) {
        this.isfolder = isfolder;
    }

   // 文件夹Id
     public Integer getFolderid() {
        return folderid;
    }

    public void setFolderid(Integer folderid) {
        this.folderid = folderid;
    }

   // 备注
     public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

   // 创建者id
     public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

   // 创建者
     public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

   // 新建日期
     public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

   // 制表id
     public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

   // 制表
     public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

   // 修改日期
     public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

   // 自定义1
     public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

   // 自定义2
     public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

   // 自定义3
     public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

   // 自定义4
     public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

   // 自定义5
     public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

   // 租户id
     public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

   // 乐观锁
     public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


    public List<SaFormitemPojo> getItem() {
        return item;
    }

    public void setItem(List<SaFormitemPojo> item) {
        this.item = item;
    }


}

