package inks.service.sa.edt.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 表单项(SaFormitem)Pojo
 *
 * <AUTHOR>
 * @since 2023-10-30 10:42:40
 */
public class SaFormitemPojo implements Serializable {
    private static final long serialVersionUID = 507013474088913347L;
      @Excel(name = "")    
  private String id;
         // 表单key
       @Excel(name = "表单key")    
  private String pid;
         // FormItemId
       @Excel(name = "FormItemId")    
  private String formitemid;
         // 表单项类型 
       @Excel(name = "表单项类型 ")    
  private String type;
         // 表单项标题
       @Excel(name = "表单项标题")    
  private String label;
         // TextLabel
       @Excel(name = "TextLabel")    
  private String textlabel;
         // 展示类型组件
       @Excel(name = "展示类型组件")    
  private Integer isdisplaytype;
         // 隐藏类型组件
       @Excel(name = "隐藏类型组件")    
  private Integer ishidetype;
         // 特殊处理类型
       @Excel(name = "特殊处理类型")    
  private Integer isspecialtype;
         // 是否显示标签
       @Excel(name = "是否显示标签")    
  private Integer showlabel;
         // 表单项默认值
       @Excel(name = "表单项默认值")    
  private String defaultvalue;
         // 是否必填
       @Excel(name = "是否必填")    
  private Integer required;
         // 输入型提示文字
       @Excel(name = "输入型提示文字")    
  private String placeholder;
         // 排序
       @Excel(name = "排序")    
  private Integer sort;
         // 栅格宽度
       @Excel(name = "栅格宽度")    
  private Integer span;
         // 表表单原始JSON
       @Excel(name = "表表单原始JSON")    
  private String scheme;
         // 正则表达式 
       @Excel(name = "正则表达式 ")    
  private String reglist;
         // 1显示列0不显示
       @Excel(name = "1显示列0不显示")    
  private Integer showcolumn;
         // 自定义1
       @Excel(name = "自定义1")    
  private String custom1;
         // 自定义2
       @Excel(name = "自定义2")    
  private String custom2;
         // 自定义3
       @Excel(name = "自定义3")    
  private String custom3;
         // 自定义4
       @Excel(name = "自定义4")    
  private String custom4;
         // 自定义5
       @Excel(name = "自定义5")    
  private String custom5;
         // 租户id
       @Excel(name = "租户id")    
  private String tenantid;
         // 乐观锁
       @Excel(name = "乐观锁")    
  private Integer revision;

  
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
     // 表单key
   
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
     // FormItemId
   
    public String getFormitemid() {
        return formitemid;
    }

    public void setFormitemid(String formitemid) {
        this.formitemid = formitemid;
    }
     // 表单项类型 
   
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
     // 表单项标题
   
    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
     // TextLabel
   
    public String getTextlabel() {
        return textlabel;
    }

    public void setTextlabel(String textlabel) {
        this.textlabel = textlabel;
    }
     // 展示类型组件
   
    public Integer getIsdisplaytype() {
        return isdisplaytype;
    }

    public void setIsdisplaytype(Integer isdisplaytype) {
        this.isdisplaytype = isdisplaytype;
    }
     // 隐藏类型组件
   
    public Integer getIshidetype() {
        return ishidetype;
    }

    public void setIshidetype(Integer ishidetype) {
        this.ishidetype = ishidetype;
    }
     // 特殊处理类型
   
    public Integer getIsspecialtype() {
        return isspecialtype;
    }

    public void setIsspecialtype(Integer isspecialtype) {
        this.isspecialtype = isspecialtype;
    }
     // 是否显示标签
   
    public Integer getShowlabel() {
        return showlabel;
    }

    public void setShowlabel(Integer showlabel) {
        this.showlabel = showlabel;
    }
     // 表单项默认值
   
    public String getDefaultvalue() {
        return defaultvalue;
    }

    public void setDefaultvalue(String defaultvalue) {
        this.defaultvalue = defaultvalue;
    }
     // 是否必填
   
    public Integer getRequired() {
        return required;
    }

    public void setRequired(Integer required) {
        this.required = required;
    }
     // 输入型提示文字
   
    public String getPlaceholder() {
        return placeholder;
    }

    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
    }
     // 排序
   
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
     // 栅格宽度
   
    public Integer getSpan() {
        return span;
    }

    public void setSpan(Integer span) {
        this.span = span;
    }
     // 表表单原始JSON
   
    public String getScheme() {
        return scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }
     // 正则表达式 
   
    public String getReglist() {
        return reglist;
    }

    public void setReglist(String reglist) {
        this.reglist = reglist;
    }
     // 1显示列0不显示
   
    public Integer getShowcolumn() {
        return showcolumn;
    }

    public void setShowcolumn(Integer showcolumn) {
        this.showcolumn = showcolumn;
    }
     // 自定义1
   
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
     // 自定义2
   
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
     // 自定义3
   
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
     // 自定义4
   
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
     // 自定义5
   
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
     // 租户id
   
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
     // 乐观锁
   
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

