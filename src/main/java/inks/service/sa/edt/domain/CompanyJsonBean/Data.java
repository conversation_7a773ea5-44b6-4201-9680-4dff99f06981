/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.edt.domain.CompanyJsonBean;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */


public class Data {

    private String state;
    private String message;
    private String special;
    private String vipMessage;
    private int isLogin;
    private int errorCode;
    @JSONField(name = "data")
    private Company company;
    private Params params;

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSpecial() {
        return special;
    }

    public void setSpecial(String special) {
        this.special = special;
    }

    public String getVipMessage() {
        return vipMessage;
    }

    public void setVipMessage(String vipMessage) {
        this.vipMessage = vipMessage;
    }

    public int getIsLogin() {
        return isLogin;
    }

    public void setIsLogin(int isLogin) {
        this.isLogin = isLogin;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public Company getCompany() {
        return company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public Params getParams() {
        return params;
    }

    public void setParams(Params params) {
        this.params = params;
    }
}