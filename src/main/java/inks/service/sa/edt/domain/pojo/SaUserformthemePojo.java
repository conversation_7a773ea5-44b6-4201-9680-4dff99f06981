package inks.service.sa.edt.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目表单项(SaUserformtheme)实体类
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
public class SaUserformthemePojo implements Serializable {
    private static final long serialVersionUID = -34292823549489579L;
    @Excel(name = "")
    private String id;
    // 表单key
    @Excel(name = "表单key")
    private String formid;
    // 提交按钮文字
    @Excel(name = "提交按钮文字")
    private String submitbtntext;
    // logo图片
    @Excel(name = "logo图片")
    private String logoimg;
    // logo位置
    @Excel(name = "logo位置")
    private String logoposition;
    // 背景颜色
    @Excel(name = "背景颜色")
    private String backcolor;
    // 背景图片
    @Excel(name = "背景图片")
    private String backimg;
    // 是否显示标题
    @Excel(name = "是否显示标题")
    private Integer showtitle;
    // 是否显示描述语
    @Excel(name = "是否显示描述语")
    private Integer showdesc;
    // 主题颜色

    @Excel(name = "主题颜色")
    private String themecolor;
    // 显示序号
    @Excel(name = "显示序号")
    private Integer shownumber;
    // 显示提交按钮
    @Excel(name = "显示提交按钮")
    private Integer showsubmitbtn;
    // 头部图片
    @Excel(name = "头部图片")
    private String headimgurl;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // 表单key

    public String getFormid() {
        return formid;
    }

    public void setFormid(String formid) {
        this.formid = formid;
    }
    // 提交按钮文字

    public String getSubmitbtntext() {
        return submitbtntext;
    }

    public void setSubmitbtntext(String submitbtntext) {
        this.submitbtntext = submitbtntext;
    }
    // logo图片

    public String getLogoimg() {
        return logoimg;
    }

    public void setLogoimg(String logoimg) {
        this.logoimg = logoimg;
    }
    // logo位置

    public String getLogoposition() {
        return logoposition;
    }

    public void setLogoposition(String logoposition) {
        this.logoposition = logoposition;
    }
    // 背景颜色

    public String getBackcolor() {
        return backcolor;
    }

    public void setBackcolor(String backcolor) {
        this.backcolor = backcolor;
    }
    // 背景图片

    public String getBackimg() {
        return backimg;
    }

    public void setBackimg(String backimg) {
        this.backimg = backimg;
    }
    // 是否显示标题

    public Integer getShowtitle() {
        return showtitle;
    }

    public void setShowtitle(Integer showtitle) {
        this.showtitle = showtitle;
    }
    // 是否显示描述语

    public Integer getShowdesc() {
        return showdesc;
    }

    public void setShowdesc(Integer showdesc) {
        this.showdesc = showdesc;
    }
    // 主题颜色


    public String getThemecolor() {
        return themecolor;
    }

    public void setThemecolor(String themecolor) {
        this.themecolor = themecolor;
    }
    // 显示序号

    public Integer getShownumber() {
        return shownumber;
    }

    public void setShownumber(Integer shownumber) {
        this.shownumber = shownumber;
    }
    // 显示提交按钮

    public Integer getShowsubmitbtn() {
        return showsubmitbtn;
    }

    public void setShowsubmitbtn(Integer showsubmitbtn) {
        this.showsubmitbtn = showsubmitbtn;
    }
    // 头部图片

    public String getHeadimgurl() {
        return headimgurl;
    }

    public void setHeadimgurl(String headimgurl) {
        this.headimgurl = headimgurl;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
    // 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
    // 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
    // 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

