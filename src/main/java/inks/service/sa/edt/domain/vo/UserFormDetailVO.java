package inks.service.sa.edt.domain.vo;

import inks.service.sa.edt.domain.pojo.SaFormPojo;
import inks.service.sa.edt.domain.pojo.SaFormitemPojo;
import inks.service.sa.edt.domain.pojo.SaFormlogicPojo;
import inks.service.sa.edt.domain.pojo.SaFormthemePojo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;


@Data
@AllArgsConstructor
public class UserFormDetailVO {
    /**
     * 表单基础信息
     */
    private SaFormPojo form;

    /**
     * 表单项
     */
    private List<SaFormitemPojo> formItems;

    /**
     * 主题
     */
    private SaFormthemePojo userFormTheme;

    /**
     * 逻辑
     */
    private SaFormlogicPojo formLogic;


}
