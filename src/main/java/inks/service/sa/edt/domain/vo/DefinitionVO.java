package inks.service.sa.edt.domain.vo;

import inks.service.sa.edt.domain.pojo.SaFormitemPojo;
import inks.service.sa.edt.domain.pojo.SaFormlogicPojo;
import inks.service.sa.edt.domain.pojo.SaFormthemePojo;
import lombok.Data;

import java.util.List;

@Data
public class DefinitionVO {
    private Integer formType;
    private List<SaFormitemPojo> formItems;
    private SaFormthemePojo userFormTheme;
    private SaFormlogicPojo userFormLogic;
}
