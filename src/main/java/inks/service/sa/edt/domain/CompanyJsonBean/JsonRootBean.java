/**
  * Copyright 2022 json.cn 
  */
package inks.service.sa.edt.domain.CompanyJsonBean;

import java.util.List;

/**
 * Auto-generated: 2022-09-06 8:37:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.json.cn/java2pojo/
 */

public class JsonRootBean {

    private Props props;
    private String page;
    private Query query;
    private String buildId;
    private String assetPrefix;
    private RuntimeConfig runtimeConfig;
    private boolean isFallback;
    private boolean gssp;
    private boolean customServer;
    private boolean appGip;
    private List<String> scriptLoader;

    public Props getProps() {
        return props;
    }

    public void setProps(Props props) {
        this.props = props;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public Query getQuery() {
        return query;
    }

    public void setQuery(Query query) {
        this.query = query;
    }

    public String getBuildId() {
        return buildId;
    }

    public void setBuildId(String buildId) {
        this.buildId = buildId;
    }

    public String getAssetPrefix() {
        return assetPrefix;
    }

    public void setAssetPrefix(String assetPrefix) {
        this.assetPrefix = assetPrefix;
    }

    public RuntimeConfig getRuntimeConfig() {
        return runtimeConfig;
    }

    public void setRuntimeConfig(RuntimeConfig runtimeConfig) {
        this.runtimeConfig = runtimeConfig;
    }

    public boolean isFallback() {
        return isFallback;
    }

    public void setFallback(boolean fallback) {
        isFallback = fallback;
    }

    public boolean isGssp() {
        return gssp;
    }

    public void setGssp(boolean gssp) {
        this.gssp = gssp;
    }

    public boolean isCustomServer() {
        return customServer;
    }

    public void setCustomServer(boolean customServer) {
        this.customServer = customServer;
    }

    public boolean isAppGip() {
        return appGip;
    }

    public void setAppGip(boolean appGip) {
        this.appGip = appGip;
    }

    public List<String> getScriptLoader() {
        return scriptLoader;
    }

    public void setScriptLoader(List<String> scriptLoader) {
        this.scriptLoader = scriptLoader;
    }
}