package inks.service.sa.edt.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.SaFormshareEntity;
import inks.service.sa.edt.domain.pojo.SaFormsharePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表单分享表(SaFormshare)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-20 14:18:23
 */
@Mapper
public interface SaFormshareMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormsharePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormsharePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saFormshareEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormshareEntity saFormshareEntity);

    
    /**
     * 修改数据
     *
     * @param saFormshareEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormshareEntity saFormshareEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
                                                                                                         }

