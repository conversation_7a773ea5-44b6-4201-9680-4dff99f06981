package inks.service.sa.edt.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.edt.domain.SaQuestionbankitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 表单项(SaQuestionbankitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-31 08:54:05
 */
 @Mapper
public interface SaQuestionbankitemMapper {

    SaQuestionbankitemPojo getEntity(@Param("key") String key);

    List<SaQuestionbankitemPojo> getPageList(QueryParam queryParam);

    List<SaQuestionbankitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaQuestionbankitemEntity saQuestionbankitemEntity);

    int update(SaQuestionbankitemEntity saQuestionbankitemEntity);

    int delete(@Param("key") String key);

    SaQuestionbankitemPojo getByFormItemId(String formItemId, String formId);
}

