package inks.service.sa.edt.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.pojo.SaQuestionbankPojo;
import inks.service.sa.edt.domain.pojo.SaQuestionbankitemdetailPojo;
import inks.service.sa.edt.domain.SaQuestionbankEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户表单(SaQuestionbank)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-31 08:53:51
 */
@Mapper
public interface SaQuestionbankMapper {

    SaQuestionbankPojo getEntity(@Param("key") String key);

    List<SaQuestionbankitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaQuestionbankPojo> getPageTh(QueryParam queryParam);

    int insert(SaQuestionbankEntity saQuestionbankEntity);

    int update(SaQuestionbankEntity saQuestionbankEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaQuestionbankPojo saQuestionbankPojo);
}

