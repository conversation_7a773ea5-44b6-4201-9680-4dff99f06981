package inks.service.sa.edt.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.SaFormlogicEntity;
import inks.service.sa.edt.domain.pojo.SaFormlogicPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目逻辑(SaFormlogic)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-17 15:02:54
 */
@Mapper
public interface SaFormlogicMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormlogicPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormlogicPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saFormlogicEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormlogicEntity saFormlogicEntity);

    
    /**
     * 修改数据
     *
     * @param saFormlogicEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormlogicEntity saFormlogicEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
                                                                                     }

