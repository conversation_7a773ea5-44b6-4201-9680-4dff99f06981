package inks.service.sa.edt.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.SaFormviewcountEntity;
import inks.service.sa.edt.domain.pojo.SaFormviewcountPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表单查看次数(SaFormviewcount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
@Mapper
public interface SaFormviewcountMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormviewcountPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormviewcountPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saFormviewcountEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormviewcountEntity saFormviewcountEntity);

    
    /**
     * 修改数据
     *
     * @param saFormviewcountEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormviewcountEntity saFormviewcountEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
                                                                                     }

