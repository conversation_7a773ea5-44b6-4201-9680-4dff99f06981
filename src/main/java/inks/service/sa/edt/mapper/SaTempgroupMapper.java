package inks.service.sa.edt.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.SaTempgroupEntity;
import inks.service.sa.edt.domain.pojo.SaTempgroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目模板分类(SaTempgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:45
 */
@Mapper
public interface SaTempgroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTempgroupPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTempgroupPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saTempgroupEntity 实例对象
     * @return 影响行数
     */
    int insert(SaTempgroupEntity saTempgroupEntity);

    
    /**
     * 修改数据
     *
     * @param saTempgroupEntity 实例对象
     * @return 影响行数
     */
    int update(SaTempgroupEntity saTempgroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
                                                                                     }

