package inks.service.sa.edt.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.SaUserformthemeEntity;
import inks.service.sa.edt.domain.pojo.SaUserformthemePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目表单项(SaUserformtheme)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:44
 */
@Mapper
public interface SaUserformthemeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaUserformthemePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaUserformthemePojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saUserformthemeEntity 实例对象
     * @return 影响行数
     */
    int insert(SaUserformthemeEntity saUserformthemeEntity);

    
    /**
     * 修改数据
     *
     * @param saUserformthemeEntity 实例对象
     * @return 影响行数
     */
    int update(SaUserformthemeEntity saUserformthemeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);
    
                                                                                                                                       }

