package inks.service.sa.edt.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.edt.domain.SaFormauthEntity;
import inks.service.sa.edt.domain.pojo.SaFormPojo;
import inks.service.sa.edt.domain.pojo.SaFormauthPojo;
import inks.service.sa.edt.domain.pojo.SaFormdataPojo;
import inks.service.sa.edt.domain.vo.AuthUsersAndDeptsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 表单授权对象(SaFormauth)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-02 16:48:40
 */
@Mapper
public interface SaFormauthMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFormauthPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFormauthPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param saFormauthEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFormauthEntity saFormauthEntity);

    
    /**
     * 修改数据
     *
     * @param saFormauthEntity 实例对象
     * @return 影响行数
     */
    int update(SaFormauthEntity saFormauthEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    AuthUsersAndDeptsVO getAuthUsersAndDepts(String formid);

    SaFormauthPojo getEntityByFormId(String formid);

    List<Map<String, String>> getUserInfo(@Param("useridList") List<String> useridList);

    List<Map<String, String>> getDeptInfo(@Param("deptidList") List<String> deptidList);

    List<String> getAllFormIdByUserid(@Param("userid") String userid);

    List<SaFormPojo> getAllFormByUserid(String userid);

    List<SaFormPojo>  getUnWriteFormByUserid(@Param("userid")String userid);

    int countAllUsersUnWrite(@Param("userid") String userid);
    List<SaFormdataPojo> getSomeFormByUserid(@Param("queryParam") QueryParam queryParam,@Param("useridList") List<String> useridList,
                                             @Param("type") String type,@Param("isAdmin") boolean isAdmin);

}

