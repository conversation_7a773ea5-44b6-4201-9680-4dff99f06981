package inks.service.sa.edt.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.sa.common.core.mapper.SaUserMapper;
import inks.service.sa.edt.mapper.SaFormMapper;
import inks.service.sa.edt.mapper.SaFormdataMapper;
import inks.service.sa.edt.service.SaFormauthService;
import inks.service.sa.edt.service.SaFormdataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户表单(SaForm)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:12:56
 */
@RestController
@RequestMapping("S18M01R1")
@Api(tags = "S18M01R1:表单报表")
public class S18M01R1Controller{

    @Resource
    private SaFormMapper saFormMapper;
    @Resource
    private SaFormdataService saFormdataService;
    @Resource
    private SaFormauthService saFormauthService;
    @Resource
    private SaFormdataMapper saFormdataMapper;
    @Resource
    private SaUserMapper saUserMapper;



    /**
     * 表单收集信息
     */
    @GetMapping("/stats")
    @ApiOperation(value = "基础信息：表单数量、表单填写数（已提交的）、表单填写率（待填写/(待填写+已填写（已提交的。同一个表单同一个人只能算一次）)）、用户总数", notes = "", produces = "application/json")
    public R<Map<String, Object>> stats() {
        Map<String, Object> resultMap = new HashMap<>();
        //用户总数
        int countUser = saUserMapper.countUser();
        resultMap.put("totalusers", countUser);
        //表单数量
        int formCount = saFormMapper.countForm();
        resultMap.put("totalforms", formCount);
        //表单填写数（submitcount已提交的,重复提交也累加）  (distinctsubmitcount已提交的。同一个表单同一个人只能算一次）
        Map<String, Object> submitMap = saFormdataMapper.countFormdataSubmit();
        resultMap.put("totalsubmit", submitMap.get("submitcount"));
        //表单填写率（待填写/(待填写+已填写（已提交的。同一个表单同一个人只能算一次）)）
        //待填写次数
        int formdataUnWrite = saFormauthService.countAllUsersUnWrite();
        Object o = submitMap.get("distinctsubmitcount");
        // 检查是否为null，然后转换为int
        int distinctSubmitCount = o != null ? ((Number) o).intValue() : 0;
        resultMap.put("totalunwrite", formdataUnWrite);
        resultMap.put("totalsubmitdistinct", o);
        resultMap.put("submitrate", (float) formdataUnWrite / (formdataUnWrite + distinctSubmitCount));

        return R.ok(resultMap);
    }


    @PostMapping("/formSubmitNumberByDay")
    @ApiOperation(value = "最近一周表单数据填写数(提交数)，按天.传入时间范围DateRange.StartDate,不传默认近7天", notes = "传入时间范围DateRange.StartDate,不传默认近7天", produces = "application/json")
    public R<List<Map<String, Object>>> formSubmitNumberByDay(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getDateRange() == null) {
            queryParam.setDateRange(new DateRange(null, DateUtils.addWeeks(new Date(), -1), new Date()));
        }
        return R.ok(saFormdataService.formSubmitNumberByDay(queryParam));
    }

    @GetMapping("/formSubmitNumberByForm")
    @ApiOperation(value = "表单总填写数（大于0的），按表单降序排序", notes = "", produces = "application/json")
    public R<List<Map<String, Object>>> formFilledInNumber() {
        return R.ok(saFormdataService.formSubmitNumberByForm());
    }

}
