package inks.service.sa.edt.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.R;
import inks.service.sa.edt.domain.pojo.SaTemplatePojo;
import inks.service.sa.edt.domain.vo.DefinitionVO;
import inks.service.sa.edt.service.SaTemplateService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 表单模板(Sa_Template)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-11 14:25:28
 */
@RestController
@RequestMapping("S18M02B1")
@Api(tags="S18M02B1:表单模板")
public class S18M02B1Controller extends SaTemplateController {
    @Resource
    private SaTemplateService saTemplateService;


    /**
     * 查询项目模板详情
     * 包含项目信息 项目保单项信息
     *
     * @param key 项目key
     * @return
     */
    @GetMapping("/details")
    public R<DefinitionVO> queryFormTemplateDetails(String key) {
        SaTemplatePojo saTemplate = saTemplateService.getEntity(key);
        String scheme = saTemplate.getScheme();
        DefinitionVO definitionVO = JSONArray.parseObject(scheme, DefinitionVO.class);
        return R.ok(definitionVO);
    }

}
