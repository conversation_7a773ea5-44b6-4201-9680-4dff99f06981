package inks.service.sa.edt.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.edt.domain.pojo.SaReportsPojo;
import inks.service.sa.edt.service.Tb_SaReportsService;
import inks.service.sa.edt.utils.FilterSqlUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 报表中心(Sa_Reports)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-04 08:58:48
 */
@RestController
@RequestMapping("TbSaReports")
@Api(tags = "通用:报表中心")
public class Tb_SaReportsController {
    /**
     * 服务对象
     */
    @Resource
    private Tb_SaReportsService tb_saReportsService;

    @Resource
    private SaRedisService saRedisService;


    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取报表中心详细信息", notes = "获取报表中心详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//////@PreAuthorize(hasPermi = "Sa_Reports.List")
    public R<SaReportsPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.tb_saReportsService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//////@PreAuthorize(hasPermi = "Sa_Reports.List")
    public R<PageInfo<SaReportsPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Reports.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            // 过滤部门
            FilterSqlUtils.filterDept(queryParam, loginUser,"Sa_Form");
            return R.ok(this.tb_saReportsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增报表中心", notes = "新增报表中心", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//////@PreAuthorize(hasPermi = "Sa_Reports.Add")
    public R<SaReportsPojo> create(@RequestBody String json) {
        try {
            SaReportsPojo saReportsPojo = JSONArray.parseObject(json, SaReportsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saReportsPojo.setCreateby(loginUser.getRealName());   // 创建者
            saReportsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saReportsPojo.setCreatedate(new Date());   // 创建时间
            saReportsPojo.setLister(loginUser.getRealname());   // 制表
            saReportsPojo.setListerid(loginUser.getUserid());    // 制表id
            saReportsPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.tb_saReportsService.insert(saReportsPojo,true));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改报表中心", notes = "修改报表中心", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//////@PreAuthorize(hasPermi = "Sa_Reports.Edit")
    public R<SaReportsPojo> update(@RequestBody String json) {
        try {
            SaReportsPojo saReportsPojo = JSONArray.parseObject(json, SaReportsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saReportsPojo.setLister(loginUser.getRealname());   // 制表
            saReportsPojo.setListerid(loginUser.getUserid());    // 制表id
            saReportsPojo.setModifydate(new Date());   //修改时间
//            saReportsPojo.setAssessor(""); // 审核员
//            saReportsPojo.setAssessorid(""); // 审核员id
//            saReportsPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.tb_saReportsService.update(saReportsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除报表中心", notes = "删除报表中心", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//////@PreAuthorize(hasPermi = "Sa_Reports.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.tb_saReportsService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//////@PreAuthorize(hasPermi = "Sa_Reports.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaReportsPojo saReportsPojo = this.tb_saReportsService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saReportsPojo);
        // 加入公司信息
        if (!Objects.isNull(loginUser.getTenantinfo())) {
            if (loginUser.getTenantinfo()!=null) inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        }
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


}

