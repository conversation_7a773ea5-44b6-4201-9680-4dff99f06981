package inks.service.sa.edt.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 表单主题分类(Sa_FormThemeGroup)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-17 15:03:43
 */
@RestController
@RequestMapping("S18M04S1")
@Api(tags="S18M04S1:项目主题外观模板")
public class S18M04S1Controller extends SaFormthemegroupController {


}
