package inks.service.sa.edt.controller.Interceptor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration // 配合ExpirationTokenInterceptor拦截器进行具体逻辑处理
public class WebMvcInterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private ExpirationTokenInterceptor tokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        /**
         * 多个拦截器组成一个拦截器链
         * 注册顺序就是拦截器执行书序
         * addPathPatterns 用于添加拦截规则，/**表示拦截所有请求
         * excludePathPatterns 用户排除拦截
         */
        registry.addInterceptor(tokenInterceptor) // 添加拦截器ExpirationTokenInterceptor
                .addPathPatterns("/**")
                .excludePathPatterns("/**/**login**/**") // 只要路径中包含login就不拦截
                .excludePathPatterns("/S18M95S1/**")
                .excludePathPatterns("/File/**")
                .excludePathPatterns("/flyway/**")
                .excludePathPatterns("/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**");
    }

    /***
     * 配置静态资源访问拦截
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry){
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
}