package inks.service.sa.edt.controller.Interceptor;

import com.alibaba.fastjson.JSON;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.sa.common.core.service.SaRedisService;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component //被WebMvcInterceptorConfig调用 用来执行具体逻辑
public class ExpirationTokenInterceptor implements HandlerInterceptor {
    @Resource
    private SaRedisService saRedisService;

    //实现了HandlerInterceptor接口。这个拦截器的作用是在处理HTTP请求之前验证用户的登录状态。
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 在这里获取用户登录信息，假设 saRedisService.getLoginUser 方法用于获取用户信息
        LoginUser loginUser = saRedisService.getLoginUser(request);
        if (loginUser == null) {
            // 令牌已过期提示
            String errorMessage = "token已过期，请重新登录";
            // 构建返回对象，并使用FastJSON将其转化为JSON字符串
            R<T> apiResult = new R();
            apiResult.setCode(HttpServletResponse.SC_UNAUTHORIZED);
            apiResult.setData(null);
            apiResult.setMsg(errorMessage);
            // 将返回对象转化为JSON字符串
            String jsonResponse = JSON.toJSONString(apiResult);
            // 设置HTTP响应的Content-Type为JSON
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            // 将JSON字符串写入响应
            response.getWriter().write(jsonResponse);
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED); // 设置 HTTP 状态码为 401
            return false; // 停止请求继续执行
        }
        return true; // 继续请求的处理
    }
}
