package inks.service.sa.edt.controller;

import inks.sa.common.core.config.oss.Storage;
import inks.sa.common.core.config.oss.service.OSSConfigManager;
import inks.service.sa.edt.domain.pojo.SaFormsharePojo;
import inks.service.sa.edt.service.SaFormshareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.xmlbeans.impl.common.IOUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;

/**
 * 表单分享表(Sa_FormShare)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-20 14:18:23
 */
@RestController
@RequestMapping("S18M09B1")
@Api(tags="S18M09B1:表单分享表")
public class S18M09B1Controller extends SaFormshareController {
    @Resource
    private SaFormshareService saFormshareService;
    @Autowired
    private OSSConfigManager configManager;
    private String BUCKET;
    @PostConstruct
    public void init() {
        this.BUCKET = configManager.getMinioBucket();
    }

    @Resource
    @Qualifier("minioStorage") // 或者使用 @Qualifier("minioStorage")
    Storage storageMinio;


    /**
     * @Description 通过分享id获取PDF
     * <AUTHOR>
     * @param[1] key 分享id:Sa_FormShare.id
     * @param[2] password 分享密码:Sa_FormShare.password
     * @time 2023/9/20 16:38
     */
    @ApiOperation(value = "通过分享id获取PDF", notes = "获取详细信息", produces = "application/json")
    @RequestMapping(value = "/getPdfByShareId", method = RequestMethod.GET)
    public void getMarkdownByShareId(String key, String password, HttpServletRequest request, HttpServletResponse response) {
        try {
            SaFormsharePojo saFormsharePojo = saFormshareService.getEntity(key);
            // 若saLnksharePojo不存在或者已经过期，则返回错误信息
            if (saFormsharePojo == null || saFormsharePojo.getDeadtime().before(new Date())) {
                throw new Exception("【表单PDF】分享不存在或已过期");
            }
            // 查看saLnksharePojo是否设置密码,若设置了密码,则对比密码是否正确,若不正确，则返回错误信息
            if (saFormsharePojo.getPasswordmark()==1) {
                if (!saFormsharePojo.getPassword().equals(password)) {
                    throw new Exception("【表单PDF】分享密码错误");
                }
            }
            //     拷贝95S1: 通用文件读取dirname/filename      ObjectName就是saFormsharePojo.getUrl()
            InputStream in = null;
            try {
                in = storageMinio.getObject(BUCKET, saFormsharePojo.getUrl());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (in == null) {
                response.sendError(404, "未能找到文件");
            }
            try {
                ServletOutputStream outputStream = response.getOutputStream();
                IOUtil.copyCompletely(in, outputStream);
                //返回前端文件流
                outputStream.flush();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        } catch (Exception e) {

        }
    }

}
