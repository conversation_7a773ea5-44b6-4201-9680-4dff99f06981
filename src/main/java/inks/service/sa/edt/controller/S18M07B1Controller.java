package inks.service.sa.edt.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.service.sa.edt.domain.pojo.SaFormPojo;
import inks.service.sa.edt.domain.pojo.SaFormdataPojo;
import inks.service.sa.edt.domain.vo.FormAuthVO;
import inks.service.sa.edt.service.SaFormauthService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 表单授权对象(Sa_FormAuth)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-31 09:49:05
 */
@RestController
@RequestMapping("S18M07B1")
@Api(tags = "S18M07B1:表单授权对象")
public class S18M07B1Controller extends SaFormauthController {
    @Resource
    private SaFormauthService saFormauthService;
    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "获取FormId下具有协作权限用户和部门", notes = "获取表单授权对象详细信息", produces = "application/json")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_FormAuth.List")
    public R<List<FormAuthVO>> list(String formid) {
        try {
            return R.ok(this.saFormauthService.getAuthUsersAndDepts(formid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取userId关联的所有FormId", notes = "获取表单授权对象详细信息", produces = "application/json")
    @RequestMapping(value = "/getAllFormIdByUserid", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_FormAuth.List")
    public R<List<String>> getAllFormIdByUserid(String userid) {
        try {
            return R.ok(this.saFormauthService.getAllFormIdByUserid(userid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取userId关联的所有Form主表信息List", notes = "获取表单授权对象详细信息", produces = "application/json")
    @RequestMapping(value = "/getAllFormByUserid", method = RequestMethod.GET)
    public R<List<SaFormPojo>> getAllFormByUserid(String userid) {
        try {
            return R.ok(this.saFormauthService.getAllFormByUserid(userid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取userId关联的所有待填写Form主表信息List 20231115改成了getAllFormByUserid的功能:获取userId关联的所有Form主表信息List", notes = "获取表单授权对象详细信息", produces = "application/json")
    @RequestMapping(value = "/getUnWriteFormByUserid", method = RequestMethod.GET)
    public R<List<SaFormPojo>> getUnWriteFormByUserid(String userid) {
        try {
            return R.ok(this.saFormauthService.getUnWriteFormByUserid(userid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @Description 1.获取条件下userId关联Form主表信息List type:unsubmit已填写未提交、submit已填写已提交、reject已提交但审核驳回、pass已提交且审核通过
     *  2.userid是否是部门管理员,若同时属于ABC三个部门,且是A,B部门管理员,C部门不是管理员,则查询自己的填表加上AB部门下的所有用户填的表;  若不是部门管理员,则只查询本人填表
     *  3.加入账号下填表次数限制
     * <AUTHOR>
     * @param[1] json
     * @param[2] userid
     * @param[3] type
     * @time 2023/9/27 12:42
     */
    @ApiOperation(value = "获取条件下userId关联FormData信息List type:unsubmit已填写未提交、submit已填写已提交、reject已提交但审核驳回、pass已提交且审核通过" +
            "超级管理员 (AdminMark=2)权限: 查询所有用户的表单数据；" +
            "部门管理员 权限: 查询自己和所管理部门下所有用户的表单数据；" +
            "普通用户 权限: 只能查询自己填写的表单数据", notes = "", produces = "application/json")
    @RequestMapping(value = "/getSomeFormByUserid", method = RequestMethod.POST)
    public R<PageInfo<SaFormdataPojo>> getSomeFormByUserid(@RequestBody String json, String userid, String type) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            if (StringUtils.isBlank(type)) {
                throw new Exception("type不能为空");
            }
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (isBlank(queryParam.getOrderBy()))  queryParam.setOrderBy("Sa_FormData.ModifyDate");
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFormauthService.getSomeFormByUserid(queryParam, userid, type));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
