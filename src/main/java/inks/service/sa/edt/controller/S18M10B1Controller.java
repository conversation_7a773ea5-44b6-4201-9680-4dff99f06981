package inks.service.sa.edt.controller;

import cn.hutool.core.util.ObjectUtil;
import inks.common.core.domain.R;
import inks.service.sa.edt.domain.pojo.SaQuestionbankitemPojo;
import inks.service.sa.edt.domain.vo.OperateFormItemVO;
import inks.service.sa.edt.domain.vo.SortFormItemRequest;
import inks.service.sa.edt.domain.vo.UserQuestionbankDetailVO;
import inks.service.sa.edt.service.SaQuestionbankService;
import inks.service.sa.edt.service.SaQuestionbankitemService;
import inks.service.sa.edt.utils.SortUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 题库(Sa_QuestionBank)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-07 16:52:43
 */
@RestController
@RequestMapping("S18M10B1")
@Api(tags = "S18M10B1:题库")
public class S18M10B1Controller extends SaQuestionbankController {

    @Resource
    private SaQuestionbankService saQuestionbankService;
    @Resource
    private SaQuestionbankitemService saQuestionbankitemService;
    @Resource
    private SortUtils sortUtils;

    @ApiOperation(value = "获取题库主子表,逻辑表,主体表 拷贝自saFormService.details", notes = "预览", produces = "application/json")
    @RequestMapping(value = "/details", method = RequestMethod.GET)
    public R<UserQuestionbankDetailVO> details(String key) {
        // 获得用户数据
//            LoginUser loginUser= saRedisService.getLoginUser(ServletUtils.getRequest());
        // 拷贝自saFormService.details
        return R.ok(this.saQuestionbankService.details(key));
    }


    @PostMapping("/sort")
    @ApiOperation(value = "题库 表单项排序", notes = "表单项排序", produces = "application/json")
    public R sortFormItem(@RequestBody SortFormItemRequest request) {
//        ValidatorUtils.validateEntity(request);/**/
        if (ObjectUtil.isNull(request.getAfterPosition()) && ObjectUtil.isNull(request.getBeforePosition())) {
            return R.ok();
        }
        SaQuestionbankitemPojo itemEntity = saQuestionbankitemService.getByFormItemId(request.getFormItemId(), request.getFormKey());
        Long sort = sortUtils.calcSortPosition(request.getBeforePosition(), request.getAfterPosition(), request.getFormKey());
        if (sortUtils.sortAllList(request.getBeforePosition(), request.getAfterPosition(), request.getFormKey(), sort)) {
            return R.ok(new OperateFormItemVO(itemEntity.getSort().longValue(), Long.parseLong(itemEntity.getId()), true, true));
        }
        itemEntity.setSort(Math.toIntExact(sort));
        saQuestionbankitemService.update(itemEntity);
        return R.ok(new OperateFormItemVO(itemEntity.getSort().longValue(), Long.parseLong(itemEntity.getId()), true, false));
    }

}
