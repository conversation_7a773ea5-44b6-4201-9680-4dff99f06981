package inks.service.sa.edt.utils.RSA;


import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

//私钥解密 Decryptor:解密器
public class RSADecryptor {
    public static String decrypt(String encryptedText, String privateKeyStr) throws Exception {
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyStr);
        PrivateKey privateKey = KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(privateKeyBytes));
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decryptedBytes);
    }


    public static void main(String[] args) {
        try {
            String privateKeyStr = MyRSA.PRIVATE_KEY;
//            String encryptedText = "Sc+NVkRfbi4TleO4HcamlwyB3HyLt8hLX45ryYUMk/dIE1U6YCt4OUEX5CMH0VbBnbdXqwpDzPSO9Mo8XpaRouJXjHUW8K1L8HrSFmGoX4FTsXCHbEqhwhQ7Hz7IA/rkyBwbaDs6f4Hfjcpe0xg07LVuDmU3RG0rVcXGcepfZJa6YEp0jmUY/QvRCkqAibUizvRlsCDnXCsFlT/sbgfs1O6CgTLI4gFhX9Zs5IxwSaKgEfCon9734ET/********************************/LMnIvbssu/0D8oivyY5dXnqDf3991dC3lgpp2cGe3SEDRW7CAAtLZUgQPApOQ==";
            String encryptedText1 = "JJlY57PwJPLydzYyqDNKuf5qCSjxZHLSxidK/18267lq4P1opa8mtrv6tJMXt6TDtjUi0u5VeQ/GhXlD/I5MvSiSNuuZp4N8g4cOHzIi7CDu1KWotzaEa8XMXbGkI70Knzp1Pzf70J7tO5McZO123WO9G8E0SjJ9KQXzMvStW8tKqEW5GAQNBPaXajWi6ZO9peyWk6hTe+Wc2wDiOauy8FwbOG5wl0OrE8sXpqQBKXBEjNR9EVlQEzse1QL0hLlCNJFhp8eMzXEbfnWEx2ks/GMWYR9sCucenQwmE8fBf0r02WqLF0TVN2UOQEFO7OYKYuCRmmb45IwKBFtGpgFslg==";
            String encryptedText2 = "JJlY57PwJPLydzYyqDNKuf5qCSjxZHLSxidK/18267lq4P1opa8mtrv6tJMXt6TDtjUi0u5VeQ/GhXlD/I5MvSiSNuuZp4N8g4cOHzIi7CDu1KWotzaEa8XMXbGkI70Knzp1Pzf70J7tO5McZO123WO9G8E0SjJ9KQXzMvStW8tKqEW5GAQNBPaXajWi6ZO9peyWk6hTe Wc2wDiOauy8FwbOG5wl0OrE8sXpqQBKXBEjNR9EVlQEzse1QL0hLlCNJFhp8eMzXEbfnWEx2ks/GMWYR9sCucenQwmE8fBf0r02WqLF0TVN2UOQEFO7OYKYuCRmmb45IwKBFtGpgFslg==";
            String decryptedText1 = decrypt(encryptedText1, privateKeyStr);
            System.out.println("Decrypted Text: " + decryptedText1);
            String decryptedText2 = decrypt(encryptedText2, privateKeyStr);
            System.out.println("Decrypted Text: " + decryptedText2);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    public static void main(String[] args) {
//        try {
//            String privateKeyStr = "your_private_key_here";
//            String encryptedText = "your_encrypted_text_here";
//            String decryptedText = decrypt(encryptedText, privateKeyStr);
//            System.out.println("Decrypted Text: " + decryptedText);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
}
