package inks.service.sa.edt.utils;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import org.apache.commons.lang3.StringUtils;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 过滤SQL 工具类
 * @param[1] null
 * @time 2023/10/10 15:22
 */
public class FilterSqlUtils {

    // (场景的下面)最下面过滤部门
    public static void filterDept(QueryParam queryParam, LoginUser loginUser) {
        String userId = loginUser.getUserid();
        // 如果是超级管理员，则不过滤,查询所有
        if (loginUser.getIsadmin() != 2) {
            // 如果是部门管理员，则能查询管理的几个部门和自己的
            if (loginUser.getTenantinfo().getIsdeptadmin() != null && loginUser.getTenantinfo().getIsdeptadmin() == 1) {
                String inDeptIds = loginUser.getTenantinfo().getLstdept().stream()
                        .map(dept -> "'" + dept.getDeptid() + "'")
                        .collect(Collectors.joining(","));
                queryParam.setFilterstr(" and (Deptid in (" + inDeptIds + ") or CreateByid = '" + userId + "') ");
            } else {
                // 如果是普通用户，则只能看自己的
                queryParam.setFilterstr(" and CreateByid = '" + userId + "' ");
            }
        }
    }


    // (场景的下面)最下面过滤部门
    // 添加了一个表名参数 tablename，如果传入了表名，则会将其拼接到字段Deptid/CreateByid前面：
    public static void filterDept(QueryParam queryParam, LoginUser loginUser, String tablename) {
        String userId = loginUser.getUserid();
        String deptid = "Deptid";
        String createByid = "CreateByid";
        // 拼接表名.字段名
        if (StringUtils.isNotBlank(tablename)) {
            deptid = tablename + "." + deptid;
            createByid = tablename + "." + createByid;
        }

        // 如果是超级管理员，则不过滤,查询所有
        if (loginUser.getIsadmin() != 2) {
            // 如果是部门管理员，则能查询管理的几个部门和自己的
            if (loginUser.getTenantinfo().getIsdeptadmin() != null && loginUser.getTenantinfo().getIsdeptadmin() == 1) {
                String inDeptIds = loginUser.getTenantinfo().getLstdept().stream()
                        .map(dept -> "'" + dept.getDeptid() + "'")
                        .collect(Collectors.joining(","));
                queryParam.setFilterstr(" and (" + deptid + " in (" + inDeptIds + ") or " + createByid + " = '" + userId + "') ");
            } else {
                // 如果是普通用户，则只能看自己的
                queryParam.setFilterstr(" and " + createByid + " = '" + userId + "' ");
            }
        }
    }


}